'use client';

import { useState, useEffect } from "react";
import { useFrappeGetCall, useFrappeGetDoc } from "frappe-react-sdk";
import { DocTypeMeta, Field } from "@/lib/frappe/frappe.types";
import { useToast } from "@/hooks/use-toast";

interface UseDocumentDataReturn {
  formData: Record<string, any>;
  formMeta: DocTypeMeta | null;
  isLoading: boolean;
  error: string | null;
  currentDocType: any;
}

export function useDocumentData(
  doctype: string, 
  docname: string, 
  isNew: boolean
): UseDocumentDataReturn {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [formMeta, setFormMeta] = useState<DocTypeMeta | null>(null);
  const { toast } = useToast();

  // Fetch Doctype metadata
  const { 
    data: doctypeMetaData, 
    error: doctypeMetaError, 
    isLoading: isLoadingMeta 
  } = useFrappeGetCall<DocTypeMeta>(
    'frappe.desk.form.load.getdoctype',
    {
      doctype: doctype,
      with_parent: 1,
    },
    {
      enabled: !!doctype,
    }
  );

  // Fetch existing document data if not a new document
  const { 
    data: docData, 
    error: docError, 
    isLoading: isLoadingDoc 
  } = useFrappeGetDoc(
    doctype,
    docname,
    {
      enabled: !!doctype && !isNew,
    }
  );

  // Process metadata and set initial form data
  useEffect(() => {
    if (doctypeMetaData) {
      setFormMeta(doctypeMetaData);
      
      if (isNew && doctypeMetaData.docs?.[0]?.fields) {
        const initialData: Record<string, any> = {};
        doctypeMetaData.docs[0].fields.forEach((field: Field) => {
          if (field.default !== undefined) {
            initialData[field.fieldname] = field.default;
          } else if (
            field.fieldtype === 'Link' && 
            doctypeMetaData.user_settings && 
            typeof field.options === 'string' && 
            doctypeMetaData.user_settings[field.options]
          ) {
            initialData[field.fieldname] = doctypeMetaData.user_settings[field.options];
          }
        });
        setFormData(initialData);
      }
    }
    
    if (doctypeMetaError) {
      toast({
        title: 'Error fetching Doctype metadata',
        description: doctypeMetaError.message,
        variant: 'destructive',
      });
    }
  }, [doctypeMetaData, doctypeMetaError, isNew, toast]);

  // Process document data
  useEffect(() => {
    if (docData) {
      setFormData(docData);
    }
    
    if (docError) {
      toast({
        title: 'Error fetching document data',
        description: docError.message,
        variant: 'destructive',
      });
    }
  }, [docData, docError, toast]);

  const currentDocType = formMeta?.docs?.[0];
  const isLoading = isLoadingMeta || (isLoadingDoc && !isNew);
  const error = doctypeMetaError?.message || docError?.message || null;

  return {
    formData,
    formMeta,
    isLoading,
    error,
    currentDocType
  };
}

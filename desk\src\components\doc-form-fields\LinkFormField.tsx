import React, { useState, useCallback, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';
import { useSearch } from 'frappe-react-sdk';

export const LinkFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  parentDoctype,
  toast,
}) => {
  const fieldValue = formData[field.fieldname] || '';
  const [inputValue, setInputValue] = useState(fieldValue);
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Sync input value with form data when it changes externally
  useEffect(() => {
    setInputValue(fieldValue);
  }, [fieldValue]);

  // Use the useSearch hook from frappe-react-sdk
  const { data: searchResults, isLoading: isSearching } = useSearch(
    field.options as string, // doctype
    searchText, // search text
    [], // filters (empty for now)
    20, // limit
    300 // debounce in ms
  );

  // Extract suggestions from search results
  const suggestions = searchResults?.message || [];

  // Handle input change - only trigger search when user types
  const handleInputChangeLocal = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    handleInputChange(field.fieldname, newValue);

    // Only search if field is not read-only and user is typing
    if (!(field.read_only === 1 && !isNew)) {
      setIsOpen(true);
      setSearchText(newValue); // This will trigger the useSearch hook
    }
  }, [field.fieldname, field.read_only, isNew, handleInputChange]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((selectedValue: string, selectedLabel?: string) => {
    setInputValue(selectedLabel || selectedValue);
    handleInputChange(field.fieldname, selectedValue);
    setIsOpen(false);
    setSearchText(''); // Clear search to stop further requests
  }, [field.fieldname, handleInputChange]);

  // Handle focus - don't trigger search automatically
  const handleFocus = useCallback(() => {
    if (field.read_only === 1 && !isNew) return;
    // Only show dropdown if there are existing suggestions
    if (suggestions && suggestions.length > 0) {
      setIsOpen(true);
    }
  }, [field.read_only, isNew, suggestions]);

  // Handle blur - close dropdown
  const handleBlur = useCallback(() => {
    // Delay to allow click on suggestion
    setTimeout(() => {
      setIsOpen(false);
    }, 150);
  }, []);

  const isReadOnly = field.read_only === 1 && !isNew;

  return (
    <div className="mb-4 relative">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>

      <Input
        id={field.fieldname}
        name={field.fieldname}
        value={inputValue}
        onChange={handleInputChangeLocal}
        onFocus={handleFocus}
        onBlur={handleBlur}
        readOnly={isReadOnly}
        disabled={isReadOnly}
        type="text"
        placeholder={isReadOnly ? inputValue : `Search ${field.options}...`}
        autoComplete="off"
        className={`mt-1 ${isReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`}
      />

      {/* Dropdown with suggestions */}
      {isOpen && suggestions.length > 0 && (
        <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
          {suggestions.map((suggestion: any, index: number) => (
            <div
              key={`${suggestion.value || suggestion.name}-${index}`}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              onMouseDown={(e) => {
                e.preventDefault();
                handleSuggestionSelect(
                  suggestion.value || suggestion.name,
                  suggestion.label || suggestion.title
                );
              }}
            >
              <div className="font-medium text-gray-900">
                {suggestion.label || suggestion.title || suggestion.value || suggestion.name}
              </div>
              {suggestion.description && (
                <div className="text-xs text-gray-500 mt-1">
                  {suggestion.description}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Loading indicator */}
      {isSearching && (
        <div className="absolute right-3 top-9 text-gray-400">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Field description */}
      {field.description && (
        <p className="mt-2 text-xs text-gray-500">{field.description}</p>
      )}
    </div>
  );
};

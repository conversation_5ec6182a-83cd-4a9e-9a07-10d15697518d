import React, { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';
import { useFrappeGetCall } from 'frappe-react-sdk';
import { debounce } from 'lodash';
import { Field } from '@/lib/frappe/frappe.types';

interface LinkSearchResult {
  value: string;
  label: string;
  description?: string;
}

export const LinkFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  parentDoctype,
  toast,
}) => {
  const fieldValue = formData[field.fieldname] || '';
  const [inputValue, setInputValue] = useState(fieldValue);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState<LinkSearchResult[]>([]);
  const [enableSearch, setEnableSearch] = useState(false);

  // Sync input value with form data when it changes externally
  useEffect(() => {
    setInputValue(fieldValue);
  }, [fieldValue]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((term: string) => {
      if (term.trim().length > 0) {
        setSearchTerm(term.trim());
        setEnableSearch(true);
      } else {
        setSearchTerm('');
        setEnableSearch(false);
        setSuggestions([]);
      }
    }, 300),
    []
  );

  // API call for search - only enabled when user is actively searching
  const { data: searchResults, error: searchError, isLoading: isSearching } = useFrappeGetCall<{ results: LinkSearchResult[] }>(
    'frappe.desk.search.search_link',
    {
      doctype: field.options as string,
      txt: searchTerm,
      reference_doctype: parentDoctype,
    },
    {
      enabled: enableSearch &&
               typeof field.options === 'string' &&
               field.options.length > 0 &&
               searchTerm.length > 0,
      onSuccess: (data: { results: LinkSearchResult[] }) => {
        setSuggestions(data.results || []);
        setEnableSearch(false); // Disable further requests until next search
      },
      onError: (err: Error) => {
        console.error('Link search error:', err);
        toast({
          title: `Error searching ${field.options}`,
          description: err.message,
          variant: 'destructive'
        });
        setSuggestions([]);
        setEnableSearch(false);
      }
    }
  );

  // Handle input change - only trigger search when user types
  const handleInputChangeLocal = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    handleInputChange(field.fieldname, newValue);

    // Only search if field is not read-only and user is typing
    if (!(field.read_only === 1 && !isNew)) {
      setIsOpen(true);
      debouncedSearch(newValue);
    }
  }, [field.fieldname, field.read_only, isNew, handleInputChange, debouncedSearch]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((selectedValue: string, selectedLabel?: string) => {
    setInputValue(selectedLabel || selectedValue);
    handleInputChange(field.fieldname, selectedValue);
    setIsOpen(false);
    setSuggestions([]);
    setSearchTerm('');
    setEnableSearch(false);
  }, [field.fieldname, handleInputChange]);

  // Handle focus - don't trigger search automatically
  const handleFocus = useCallback(() => {
    if (field.read_only === 1 && !isNew) return;
    // Only show dropdown if there are existing suggestions
    if (suggestions.length > 0) {
      setIsOpen(true);
    }
  }, [field.read_only, isNew, suggestions.length]);

  // Handle blur - close dropdown
  const handleBlur = useCallback(() => {
    // Delay to allow click on suggestion
    setTimeout(() => {
      setIsOpen(false);
    }, 150);
  }, []);

  const isReadOnly = field.read_only === 1 && !isNew;

  return (
    <div className="mb-4 relative">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>

      <Input
        id={field.fieldname}
        name={field.fieldname}
        value={inputValue}
        onChange={handleInputChangeLocal}
        onFocus={handleFocus}
        onBlur={handleBlur}
        readOnly={isReadOnly}
        disabled={isReadOnly}
        type="text"
        placeholder={isReadOnly ? inputValue : `Search ${field.options}...`}
        autoComplete="off"
        className={`mt-1 ${isReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`}
      />

      {/* Dropdown with suggestions */}
      {isOpen && suggestions.length > 0 && (
        <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.value}-${index}`}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              onMouseDown={(e) => {
                e.preventDefault();
                handleSuggestionSelect(suggestion.value, suggestion.label);
              }}
            >
              <div className="font-medium text-gray-900">
                {suggestion.label || suggestion.value}
              </div>
              {suggestion.description && (
                <div className="text-xs text-gray-500 mt-1">
                  {suggestion.description}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Loading indicator */}
      {isSearching && searchTerm.length > 0 && (
        <div className="absolute right-3 top-9 text-gray-400">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Field description */}
      {field.description && (
        <p className="mt-2 text-xs text-gray-500">{field.description}</p>
      )}
    </div>
  );
};

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';

interface LinkSearchResult {
  value: string;
  label: string;
  description?: string;
}

export const LinkFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  parentDoctype,
  toast,
}) => {
  const fieldValue = formData[field.fieldname] || '';
  const [inputValue, setInputValue] = useState(fieldValue);
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<LinkSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Sync input value with form data when it changes externally
  useEffect(() => {
    setInputValue(fieldValue);
  }, [fieldValue]);

  // Manual search function
  const performSearch = useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim() || typeof field.options !== 'string') {
      setSuggestions([]);
      return;
    }

    setIsSearching(true);

    try {
      const response = await fetch('/api/method/frappe.desk.search.search_link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          doctype: field.options,
          txt: searchTerm.trim(),
          reference_doctype: parentDoctype,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message && Array.isArray(data.message.results)) {
        setSuggestions(data.message.results);
      } else {
        setSuggestions([]);
      }
    } catch (error: any) {
      console.error('Search error:', error);
      toast({
        title: `Error searching ${field.options}`,
        description: error.message || 'Search failed',
        variant: 'destructive'
      });
      setSuggestions([]);
    } finally {
      setIsSearching(false);
    }
  }, [field.options, parentDoctype, toast]);

  // Debounced search with timeout
  const handleInputChangeLocal = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    handleInputChange(field.fieldname, newValue);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Only search if field is not read-only and user is typing
    if (!(field.read_only === 1 && !isNew)) {
      setIsOpen(true);

      // Set new timeout for search
      searchTimeoutRef.current = setTimeout(() => {
        performSearch(newValue);
      }, 300);
    }
  }, [field.fieldname, field.read_only, isNew, handleInputChange, performSearch]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((selectedValue: string, selectedLabel?: string) => {
    setInputValue(selectedLabel || selectedValue);
    handleInputChange(field.fieldname, selectedValue);
    setIsOpen(false);
    setSuggestions([]);

    // Clear any pending search
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
  }, [field.fieldname, handleInputChange]);

  // Handle focus - don't trigger search automatically
  const handleFocus = useCallback(() => {
    if (field.read_only === 1 && !isNew) return;
    // Only show dropdown if there are existing suggestions
    if (suggestions.length > 0) {
      setIsOpen(true);
    }
  }, [field.read_only, isNew, suggestions.length]);

  // Handle blur - close dropdown
  const handleBlur = useCallback(() => {
    // Delay to allow click on suggestion
    setTimeout(() => {
      setIsOpen(false);
    }, 150);
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const isReadOnly = field.read_only === 1 && !isNew;

  return (
    <div className="mb-4 relative">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>

      <Input
        id={field.fieldname}
        name={field.fieldname}
        value={inputValue}
        onChange={handleInputChangeLocal}
        onFocus={handleFocus}
        onBlur={handleBlur}
        readOnly={isReadOnly}
        disabled={isReadOnly}
        type="text"
        placeholder={isReadOnly ? inputValue : `Search ${field.options}...`}
        autoComplete="off"
        className={`mt-1 ${isReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`}
      />

      {/* Dropdown with suggestions */}
      {isOpen && suggestions.length > 0 && (
        <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.value}-${index}`}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              onMouseDown={(e) => {
                e.preventDefault();
                handleSuggestionSelect(suggestion.value, suggestion.label);
              }}
            >
              <div className="font-medium text-gray-900">
                {suggestion.label || suggestion.value}
              </div>
              {suggestion.description && (
                <div className="text-xs text-gray-500 mt-1">
                  {suggestion.description}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Loading indicator */}
      {isSearching && (
        <div className="absolute right-3 top-9 text-gray-400">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Field description */}
      {field.description && (
        <p className="mt-2 text-xs text-gray-500">{field.description}</p>
      )}
    </div>
  );
};

import React, { useState, useCallback, useRef, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';
import { useFrappeGetCall } from 'frappe-react-sdk';
import { debounce } from 'lodash';
import { Field } from '@/lib/frappe/frappe.types';

interface LinkSearchResult {
  value: string;
  label: string;
  description?: string;
}

export const LinkFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  parentDoctype,
  toast,
}) => {
  const fieldValue = formData[field.fieldname] || '';
  const [isFocused, setIsFocused] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<LinkSearchResult[]>([]);
  const [shouldFetch, setShouldFetch] = useState(false);

  // Use ref to track if user is actively typing
  const isTypingRef = useRef(false);

  // Memoize debounced search to prevent recreation on every render
  const debouncedSearch = useMemo(
    () => debounce((term: string) => {
      if (term.length > 0 && typeof field.options === 'string' && field.options.length > 0) {
        setSearchQuery(term);
        setShouldFetch(true);
      } else {
        setSuggestions([]);
        setSearchQuery('');
        setShouldFetch(false);
      }
    }, 300),
    [field.options]
  );

  const { data: fetchedLinkData, error: linkFetchError, isLoading: isLoadingLinkSuggestions } = useFrappeGetCall<{ results: LinkSearchResult[] }>(
    'frappe.desk.search.search_link',
    {
      doctype: field.options as string,
      txt: searchQuery,
      reference_doctype: parentDoctype,
    },
    {
      enabled: shouldFetch &&
               isFocused &&
               isTypingRef.current &&
               typeof field.options === 'string' &&
               field.options.length > 0 &&
               searchQuery.length > 0,
      onSuccess: (data: { results: LinkSearchResult[] }) => {
        setSuggestions(data.results || []);
        setShouldFetch(false); // Stop fetching after successful response
        isTypingRef.current = false; // Reset typing flag
      },
      onError: (err: Error) => {
        toast({
          title: `Error fetching ${field.options} list`,
          description: err.message,
          variant: 'destructive'
        });
        setSuggestions([]);
        setShouldFetch(false); // Stop fetching on error
        isTypingRef.current = false; // Reset typing flag
      }
    }
  );

  const onFieldChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    handleInputChange(field.fieldname, newValue);

    // Only trigger search when user is actively typing
    if (typeof field.options === 'string' && !(field.read_only === 1 && !isNew)) {
      isTypingRef.current = true; // Mark that user is typing
      debouncedSearch(newValue);
    }
  }, [field.fieldname, field.options, field.read_only, isNew, handleInputChange, debouncedSearch]);

  const onSuggestionSelect = useCallback((value: string) => {
    handleInputChange(field.fieldname, value);
    setSuggestions([]);
    setSearchQuery('');
    setIsFocused(false);
    setShouldFetch(false);
    isTypingRef.current = false; // Reset typing flag
  }, [field.fieldname, handleInputChange]);

  const handleFocus = useCallback(() => {
    if (field.read_only === 1 && !isNew) return;
    setIsFocused(true);
    // Don't auto-search on focus, only when user types
    setSuggestions([]);
    setSearchQuery('');
    setShouldFetch(false);
    isTypingRef.current = false;
  }, [field.read_only, isNew]);

  const handleBlur = useCallback(() => {
    setTimeout(() => {
      if (!document.activeElement?.closest('.link-suggestion-item')) {
        setIsFocused(false);
        setShouldFetch(false);
        isTypingRef.current = false;
      }
    }, 200);
  }, []);

  const inputProps = {
    id: field.fieldname,
    name: field.fieldname,
    value: fieldValue,
    onChange: onFieldChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    readOnly: field.read_only === 1 && !isNew,
    className: `mt-1 block w-full border border-gray-300 rounded-md p-2 ${(field.read_only === 1 && !isNew) ? 'bg-gray-100 cursor-not-allowed' : ''}`,
    disabled: field.read_only === 1 && !isNew,
    type: 'text',
    placeholder: (field.read_only === 1 && !isNew) ? fieldValue : `Search for ${field.options}`,
    autoComplete: "off",
  };

  return (
    <div className="mb-4 relative">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>
      <Input {...inputProps} />
      {isFocused && suggestions.length > 0 && (
        <div className="absolute z-20 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg link-suggestions-container">
          {suggestions.map(suggestion => (
            <div
              key={suggestion.value}
              className="p-2 hover:bg-gray-100 cursor-pointer link-suggestion-item"
              onMouseDown={(e) => { 
                e.preventDefault();
                onSuggestionSelect(suggestion.value);
              }}
            >
              {suggestion.label ? `${suggestion.label} (${suggestion.value})` : suggestion.value}
              {suggestion.description && <span className="text-xs text-gray-500 ml-2">{suggestion.description}</span>}
            </div>
          ))}
        </div>
      )}
      {isFocused && isLoadingLinkSuggestions && searchQuery.length > 0 &&
        <p className="text-xs text-gray-500 mt-1">Loading suggestions...</p>
      }
      {field.description && <p className="mt-2 text-xs text-gray-500">{field.description}</p>}
    </div>
  );
};

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';
import { useFrappeGetCall } from 'frappe-react-sdk';
import { debounce } from 'lodash';

interface LinkSearchResult {
  value: string;
  label: string;
  description?: string;
}

export const LinkFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  parentDoctype,
  toast,
}) => {
  const fieldValue = formData[field.fieldname] || '';
  const [inputValue, setInputValue] = useState(fieldValue);
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<LinkSearchResult[]>([]);

  // Search control states
  const [searchQuery, setSearchQuery] = useState('');
  const [shouldSearch, setShouldSearch] = useState(false);
  const [userIsTyping, setUserIsTyping] = useState(false);

  // Sync input value with form data when it changes externally
  useEffect(() => {
    setInputValue(fieldValue);
  }, [fieldValue]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      if (query.trim().length > 0 && userIsTyping) {
        setSearchQuery(query.trim());
        setShouldSearch(true);
      } else {
        setSearchQuery('');
        setShouldSearch(false);
        setSuggestions([]);
      }
    }, 300),
    [userIsTyping]
  );

  // useFrappeGetCall for search
  const { isLoading: isSearching } = useFrappeGetCall<{ results: LinkSearchResult[] }>(
    'frappe.desk.search.search_link',
    {
      doctype: field.options as string,
      txt: searchQuery,
      reference_doctype: parentDoctype,
    },
    {
      enabled: shouldSearch &&
               typeof field.options === 'string' &&
               field.options.length > 0 &&
               searchQuery.length > 0 &&
               userIsTyping,
      onSuccess: (data: { results: LinkSearchResult[] }) => {
        setSuggestions(data.results || []);
        setShouldSearch(false); // Disable further requests until next search
        setUserIsTyping(false); // Reset typing flag
      },
      onError: (err: any) => {
        console.error('Link search error:', err);
        toast({
          title: `Error searching ${field.options}`,
          description: err.message || 'Search failed',
          variant: 'destructive'
        });
        setSuggestions([]);
        setShouldSearch(false);
        setUserIsTyping(false);
      }
    }
  );

  // Handle input change - only trigger search when user types
  const handleInputChangeLocal = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    handleInputChange(field.fieldname, newValue);

    // Only search if field is not read-only and user is typing
    if (!(field.read_only === 1 && !isNew)) {
      setIsOpen(true);
      setUserIsTyping(true); // Mark that user is actively typing
      debouncedSearch(newValue);
    }
  }, [field.fieldname, field.read_only, isNew, handleInputChange, debouncedSearch]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((selectedValue: string, selectedLabel?: string) => {
    setInputValue(selectedLabel || selectedValue);
    handleInputChange(field.fieldname, selectedValue);
    setIsOpen(false);
    setSuggestions([]);
    setSearchQuery('');
    setShouldSearch(false);
    setUserIsTyping(false);
  }, [field.fieldname, handleInputChange]);

  // Handle focus - don't trigger search automatically
  const handleFocus = useCallback(() => {
    if (field.read_only === 1 && !isNew) return;
    // Only show dropdown if there are existing suggestions
    if (suggestions.length > 0) {
      setIsOpen(true);
    }
  }, [field.read_only, isNew, suggestions.length]);

  // Handle blur - close dropdown
  const handleBlur = useCallback(() => {
    // Delay to allow click on suggestion
    setTimeout(() => {
      setIsOpen(false);
    }, 150);
  }, []);

  const isReadOnly = field.read_only === 1 && !isNew;

  return (
    <div className="mb-4 relative">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>

      <Input
        id={field.fieldname}
        name={field.fieldname}
        value={inputValue}
        onChange={handleInputChangeLocal}
        onFocus={handleFocus}
        onBlur={handleBlur}
        readOnly={isReadOnly}
        disabled={isReadOnly}
        type="text"
        placeholder={isReadOnly ? inputValue : `Search ${field.options}...`}
        autoComplete="off"
        className={`mt-1 ${isReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`}
      />

      {/* Dropdown with suggestions */}
      {isOpen && suggestions.length > 0 && (
        <div className="absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-60 overflow-y-auto shadow-lg">
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.value}-${index}`}
              className="px-3 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              onMouseDown={(e) => {
                e.preventDefault();
                handleSuggestionSelect(suggestion.value, suggestion.label);
              }}
            >
              <div className="font-medium text-gray-900">
                {suggestion.label || suggestion.value}
              </div>
              {suggestion.description && (
                <div className="text-xs text-gray-500 mt-1">
                  {suggestion.description}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Loading indicator */}
      {isSearching && (
        <div className="absolute right-3 top-9 text-gray-400">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Field description */}
      {field.description && (
        <p className="mt-2 text-xs text-gray-500">{field.description}</p>
      )}
    </div>
  );
};

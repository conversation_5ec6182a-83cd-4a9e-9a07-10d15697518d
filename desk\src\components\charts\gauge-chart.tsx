import React from 'react';

export interface GaugeChartProps {
  value: number;
  min?: number;
  max?: number;
  label?: string;
  size?: number;
  thickness?: number;
  valueFormatter?: (value: number) => string;
  color?: string;
  backgroundColor?: string;
}

export const GaugeChart = ({
  value,
  min = 0,
  max = 100,
  label,
  size = 200,
  thickness = 16,
  valueFormatter = (val) => `${val}%`,
  color = '#3b82f6', // blue-500
  backgroundColor = '#e5e7eb', // gray-200
}: GaugeChartProps) => {
  // Normalize the value between 0 and 1
  const normalizedValue = Math.max(min, Math.min(value, max));
  const percentage = max === min ? 0 : (normalizedValue - min) / (max - min);
  
  // Calculate gauge properties
  const radius = (size - thickness) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference * (1 - percentage * 0.75); // 75% of the circle (270 degrees)
  
  const centerX = size / 2;
  const centerY = size / 2;
  
  return (
    <div className="flex flex-col items-center justify-center">
      <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        {/* Background track */}
        <circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="none"
          stroke={backgroundColor}
          strokeWidth={thickness}
          strokeDasharray={circumference}
          strokeDashoffset={circumference * 0.25} // Start at 25% (90 degrees)
          transform={`rotate(-90 ${centerX} ${centerY})`}
        />
        
        {/* Foreground track */}
        <circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="none"
          stroke={color}
          strokeWidth={thickness}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset + circumference * 0.25}
          transform={`rotate(-90 ${centerX} ${centerY})`}
        />
        
        {/* Value text */}
        <text
          x={centerX}
          y={centerY}
          dy="-0.5em"
          textAnchor="middle"
          className="text-2xl font-bold"
          fill="currentColor"
        >
          {valueFormatter(normalizedValue)}
        </text>
        
        {/* Label text */}
        {label && (
          <text
            x={centerX}
            y={centerY}
            dy="1.5em"
            textAnchor="middle"
            className="text-sm text-gray-500"
            fill="currentColor"
          >
            {label}
          </text>
        )}
      </svg>
    </div>
  );
};

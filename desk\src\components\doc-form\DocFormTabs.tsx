'use client';

import React from "react";
import { ProcessedTab } from "@/hooks/useFormLayout";

interface DocFormTabsProps {
  tabs: ProcessedTab[];
  activeTabId: string | null;
  onTabChange: (tabId: string) => void;
}

export function DocFormTabs({ tabs, activeTabId, onTabChange }: DocFormTabsProps) {
  return (
    <nav className="w-48 md:w-56 bg-slate-100 border-r border-slate-200 overflow-y-auto p-3 space-y-1 shrink-0">
      {tabs.map(tab => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-50
            ${activeTabId === tab.id 
              ? 'bg-sky-600 text-white font-medium shadow-sm'
              : 'text-slate-700 hover:bg-slate-200 hover:text-slate-900 font-normal'}`}
        >
          {tab.label}
        </button>
      ))}
    </nav>
  );
}

'use client';

import React from "react";
import { ProcessedTab } from "@/hooks/useFormLayout";

interface DocFormTabsProps {
  tabs: ProcessedTab[];
  activeTabId: string | null;
  onTabChange: (tabId: string) => void;
}

export function DocFormTabs({ tabs, activeTabId, onTabChange }: DocFormTabsProps) {
  return (
    <nav className="w-72 bg-slate-50/80 border-r border-slate-200/60 overflow-y-auto shrink-0 rounded-tr-2xl">
      <div className="p-6">
        <h3 className="text-xs font-semibold text-slate-500 uppercase tracking-wider mb-4">
          Form Sections
        </h3>
        <div className="space-y-2">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`w-full text-left px-4 py-3 rounded-xl text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
                ${activeTabId === tab.id
                  ? 'bg-white text-blue-700 font-medium border border-blue-200/60 shadow-sm'
                  : 'text-slate-700 hover:bg-white/60 hover:text-slate-900 font-normal border border-transparent'}`}
            >
              <div className="flex items-center">
                <div className={`w-2.5 h-2.5 rounded-full mr-3 transition-colors ${
                  activeTabId === tab.id ? 'bg-blue-500' : 'bg-slate-300'
                }`} />
                {tab.label}
              </div>
              {tab.description && (
                <p className="text-xs text-slate-500 mt-1.5 ml-5.5">{tab.description}</p>
              )}
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
}

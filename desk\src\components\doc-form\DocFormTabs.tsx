'use client';

import React from "react";
import { ProcessedTab } from "@/hooks/useFormLayout";

interface DocFormTabsProps {
  tabs: ProcessedTab[];
  activeTabId: string | null;
  onTabChange: (tabId: string) => void;
}

export function DocFormTabs({ tabs, activeTabId, onTabChange }: DocFormTabsProps) {
  return (
    <nav className="w-64 bg-white border-r border-slate-200 overflow-y-auto shrink-0">
      <div className="p-4">
        <h3 className="text-xs font-semibold text-slate-500 uppercase tracking-wider mb-3">
          Form Sections
        </h3>
        <div className="space-y-1">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`w-full text-left px-3 py-2.5 rounded-lg text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
                ${activeTabId === tab.id
                  ? 'bg-blue-50 text-blue-700 font-medium border border-blue-200 shadow-sm'
                  : 'text-slate-700 hover:bg-slate-50 hover:text-slate-900 font-normal border border-transparent'}`}
            >
              <div className="flex items-center">
                <div className={`w-2 h-2 rounded-full mr-3 ${
                  activeTabId === tab.id ? 'bg-blue-500' : 'bg-slate-300'
                }`} />
                {tab.label}
              </div>
              {tab.description && (
                <p className="text-xs text-slate-500 mt-1 ml-5">{tab.description}</p>
              )}
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
}

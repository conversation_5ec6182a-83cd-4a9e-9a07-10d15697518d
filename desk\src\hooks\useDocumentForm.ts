'use client';

import { useState, useCallback } from "react";
import { useFrappeCreateDoc, useFrappeUpdateDoc } from "frappe-react-sdk";
import { DocTypeMeta, Field } from "@/lib/frappe/frappe.types";
import { useToast } from "@/hooks/use-toast";

interface UseDocumentFormProps {
  doctype: string;
  docname: string;
  isNew: boolean;
  formData: Record<string, any>;
  formMeta: DocTypeMeta | null;
  onSuccess?: () => void;
}

interface UseDocumentFormReturn {
  handleInputChange: (fieldname: string, value: any) => void;
  handleSubmit: () => Promise<void>;
  isSubmitting: boolean;
}

export function useDocumentForm({
  doctype,
  docname,
  isNew,
  formData,
  formMeta,
  onSuccess
}: UseDocumentFormProps): UseDocumentFormReturn {
  const [localFormData, setLocalFormData] = useState(formData);
  const { toast } = useToast();
  
  // Create and Update hooks
  const { createDoc, loading: creatingDoc } = useFrappeCreateDoc();
  const { updateDoc, loading: updatingDoc } = useFrappeUpdateDoc();

  const isSubmitting = creatingDoc || updatingDoc;

  const handleInputChange = useCallback((fieldname: string, value: any) => {
    setLocalFormData(prev => ({ ...prev, [fieldname]: value }));
  }, []);

  const validateRequiredFields = useCallback((): boolean => {
    const currentDocTypeMeta = formMeta?.docs?.[0];
    if (!currentDocTypeMeta) {
      toast({ 
        title: 'Form metadata not loaded', 
        variant: 'destructive' 
      });
      return false;
    }

    // Check required fields
    for (const field of currentDocTypeMeta.fields) {
      if (
        field.reqd && 
        !field.hidden && 
        (localFormData[field.fieldname] === undefined || 
         localFormData[field.fieldname] === '' || 
         localFormData[field.fieldname] === null)
      ) {
        toast({
          title: 'Missing Required Field',
          description: `Please fill in "${field.label || field.fieldname}".`,
          variant: 'destructive',
        });
        
        // Focus the field if possible
        const fieldElement = document.getElementById(field.fieldname);
        fieldElement?.focus();
        return false;
      }
    }

    return true;
  }, [formMeta, localFormData, toast]);

  const handleSubmit = useCallback(async () => {
    if (!validateRequiredFields()) {
      return;
    }

    try {
      if (isNew) {
        await createDoc(doctype, localFormData);
        toast({
          title: 'Document Created',
          description: `${doctype} created successfully.`,
        });
      } else {
        await updateDoc(doctype, docname, localFormData);
        toast({
          title: 'Document Updated',
          description: `${doctype} ${docname} updated successfully.`,
        });
      }
      
      onSuccess?.();
    } catch (error: any) {
      toast({
        title: `Error ${isNew ? 'creating' : 'updating'} document`,
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  }, [
    validateRequiredFields,
    isNew,
    createDoc,
    updateDoc,
    doctype,
    docname,
    localFormData,
    toast,
    onSuccess
  ]);

  return {
    handleInputChange,
    handleSubmit,
    isSubmitting
  };
}

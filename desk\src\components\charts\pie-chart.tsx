import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

export interface PieChartProps {
  data: Array<{
    name: string;
    value: number;
    color?: string;
  }>;
  dataKey?: string;
  nameKey?: string;
  height?: number | string;
  tooltip?: boolean;
  legend?: boolean;
  innerRadius?: number | string;
  outerRadius?: number | string;
  paddingAngle?: number;
  valueFormatter?: (value: number) => string;
}

export const PieChart = ({
  data,
  dataKey = 'value',
  nameKey = 'name',
  height = 300,
  tooltip = true,
  legend = true,
  innerRadius = 0,
  outerRadius = '80%',
  paddingAngle = 0,
  valueFormatter,
}: PieChartProps) => {
  // Default colors with tailwind colors
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
    '#0ea5e9', // sky-500
    '#14b8a6', // teal-500
    '#ef4444', // red-500
    '#a855f7', // purple-500
  ];

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsPieChart>
        {tooltip && <Tooltip 
          formatter={(value: number) => valueFormatter ? valueFormatter(value) : value}
          contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', borderRadius: '0.375rem' }}
        />}
        {legend && <Legend wrapperStyle={{ paddingTop: '10px', fontSize: '12px' }} />}
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          paddingAngle={paddingAngle}
          dataKey={dataKey}
          nameKey={nameKey}
          labelLine={false}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color || defaultColors[index % defaultColors.length]} 
            />
          ))}
        </Pie>
      </RechartsPieChart>
    </ResponsiveContainer>
  );
};

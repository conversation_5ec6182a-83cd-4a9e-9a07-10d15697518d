// /workspaces/frappe-bench/apps/iot_gateway/desk/src/app/[doctype]/page.tsx
"use client"; // Add this for client component

import DataTable, { ColumnDefinition } from '../../components/data-table'; // Import ColumnDefinition
import { useFrappeGetDocList, useFrappeGetCall } from 'frappe-react-sdk';
import React, { useEffect, useState } from 'react';
import { AppLayout } from '../../components/layout/app-layout';
import LoadingSpinner from '../../components/ui/loading-spinner';
import { useRouter } from 'next/navigation'; // Import useRouter

import { Button } from '../../components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '../../components/ui/dialog';
import { Plus<PERSON>ircle, Trash, Eye } from 'lucide-react'; // Icons
import { useFrappeDeleteDoc } from 'frappe-react-sdk'; // Import for deleting documents
import { useToast } from '@/hooks/use-toast';

// Define a generic type for Frappe document data
interface FrappeDoc {
  name: string;
  owner: string;
  modified: string;
  creation: string;
  [key: string]: any; // Allow other fields
}

// Define an interface for the field definition from getdoctype
interface FrappeDocField {
  fieldname: string;
  label: string;
  in_list_view: number;
  idx: number;
  hidden: number;
  // Add other properties if needed
}

interface DoctypePageProps {
  params: Promise<{
    doctype: string;
  }>;
}

export default function DoctypePage({ params }: DoctypePageProps) {
  const { doctype: routeDoctype } = React.use(params); // Original value from route
  const doctype = decodeURIComponent(routeDoctype); // Ensure doctype name is decoded

  const doctypeDisplayName = doctype.replace(/[^a-zA-Z0-9_ ]/g, "").charAt(0).toUpperCase() + doctype.slice(1);

  const [dynamicTableColumns, setDynamicTableColumns] = useState<ColumnDefinition<FrappeDoc>[]>([]);
  const [fieldsToFetch, setFieldsToFetch] = useState<string[]>(["name", "owner", "modified", "creation"]);

  const router = useRouter();
  const { toast } = useToast();
  const { deleteDoc, loading: deleteLoading } = useFrappeDeleteDoc();

  const [selectedDocNames, setSelectedDocNames] = useState<string[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [docsToDelete, setDocsToDelete] = useState<string[]>([]);


  const { data: doctypeMetaData, isLoading: isLoadingMetaData, error: errorMetaData } = useFrappeGetCall<{ docs: { fields: FrappeDocField[] }[] }>(
    'frappe.desk.form.load.getdoctype',
    { doctype: doctype, with_parent: 1 }, // Use the decoded doctype
    `getdoctype-${doctype}` // SWR key also uses decoded doctype
  );

  useEffect(() => {
    if (doctypeMetaData?.docs?.[0]?.fields) {
      const fields = doctypeMetaData.docs[0].fields;
      let generatedColumns: ColumnDefinition<FrappeDoc>[] = fields
        .filter(field => field.in_list_view === 1 && field.hidden !== 1)
        .sort((a, b) => a.idx - b.idx)
        .map(field => ({
          key: field.fieldname,
          title: field.label,
          sortable: true, // Make columns sortable by default
          filterable: true, // Make columns filterable by default
        }));

      if (generatedColumns.length === 0) {
        generatedColumns = [{ key: 'name', title: 'Name', sortable: true, filterable: true }];
      }

      if (!generatedColumns.find(col => col.key === 'name')) {
        generatedColumns.unshift({ key: 'name', title: 'Name', sortable: true, filterable: true });
      }
      // Add modified as a default sortable column if not present
      if (!generatedColumns.find(col => col.key === 'modified')) {
        generatedColumns.push({ key: 'modified', title: 'Last Modified', sortable: true, filterable: true, render: (doc) => new Date(doc.modified).toLocaleString() });
      }


      setDynamicTableColumns(generatedColumns);
      const newFieldsToFetch = Array.from(new Set([
        ...generatedColumns.map(col => col.key as string),
        "name", "owner", "modified", "creation" // Ensure essential fields are fetched
      ]));
      setFieldsToFetch(newFieldsToFetch);
    } else if (!isLoadingMetaData && !errorMetaData) {
      const defaultCols: ColumnDefinition<FrappeDoc>[] = [
        { key: 'name', title: 'Name', sortable: true, filterable: true },
        { key: 'modified', title: 'Last Modified', sortable: true, filterable: true, render: (doc) => new Date(doc.modified).toLocaleString() }
      ];
      setDynamicTableColumns(defaultCols);
      setFieldsToFetch(["name", "owner", "modified", "creation"]);
    }
  }, [doctypeMetaData, isLoadingMetaData, errorMetaData, doctype]);

  const { data, isLoading: isLoadingDocList, error: errorDocList, mutate } = useFrappeGetDocList<FrappeDoc>(doctype, {
    fields: fieldsToFetch,
    limit: 20,
    orderBy: { field: 'modified', order: 'desc' },
  }, {
    enabled: dynamicTableColumns.length > 0, // Only fetch doc list if columns are defined
  });

  const isLoading = isLoadingMetaData || (dynamicTableColumns.length > 0 && isLoadingDocList);
  const error = errorMetaData || errorDocList;

  const handleRowSelectionChange = (selectedIds: string[]) => {
    setSelectedDocNames(selectedIds);
  };

  const createAction = {
    label: `New ${doctypeDisplayName}`,
    icon: <PlusCircle className="h-4 w-4" />,
    onClick: () => {
      router.push(`/app/${doctype}/new`); // Standard Frappe route for new document
    },
  };

  const selectedRowActions = [
    {
      label: 'Open',
      icon: <Eye className="h-4 w-4" />,
      onClick: (ids: string[]) => {
        if (ids.length === 1) {
          router.push(`/app/${doctype}/${ids[0]}`); // Standard Frappe route for document view
        } else {
          toast({
            title: "Multiple Selection",
            description: "Please select only one document to open.",
            type: "warning",
          });
        }
      },
    },
    {
      label: 'Delete',
      icon: <Trash className="h-4 w-4" />,
      variant: 'destructive' as const,
      onClick: (ids: string[]) => {
        setDocsToDelete(ids);
        setDeleteDialogOpen(true);
      },
    },
  ];

  const rowClickAction = {
    type: 'route' as const, // Or 'handler' if you want to define a custom function
    handler: (rowData: FrappeDoc) => {
      router.push(`/${doctype}/${rowData.name}`);
    },
  };

  const confirmDelete = async () => {
    if (docsToDelete.length === 0) return;
    try {
      for (const docName of docsToDelete) {
        await deleteDoc(doctype, docName);
      }
      toast({
        title: "Success",
        description: `${docsToDelete.length} document(s) deleted successfully.`,
        type: "success",
      });
      mutate(); // Refetch data
      setSelectedDocNames([]); // Clear selection
    } catch (err: any) {
      toast({
        title: "Error Deleting Documents",
        description: err.message || "Could not delete documents.",
        type: "error",
      });
    } finally {
      setDeleteDialogOpen(false);
      setDocsToDelete([]);
    }
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <LoadingSpinner size="lg" text={`Loading data for ${doctypeDisplayName}...`} />
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="container mx-auto py-10 px-4 sm:px-6 lg:px-8 text-center text-red-500">
          <p>Error loading data for {doctypeDisplayName}: {error?.message || 'Unknown error'}</p>
        </div>
      </AppLayout>
    );
  }

  const tableData = data || [];

  return (
    <AppLayout>
      <div className="w-full h-full flex flex-col"> {/* Ensure flex-col for h-full to work with DataTable */}
        {dynamicTableColumns.length > 0 ? (
          <DataTable
            columns={dynamicTableColumns}
            data={tableData}
            rowIdKey="name"
            enableRowSelection={true}
            onRowSelectionChange={handleRowSelectionChange}
            selectedRowActions={selectedRowActions}
            createAction={createAction}
            rowClickAction={rowClickAction}
            // customFilters can be added here if needed in the future
          />
        ) : (
          <div className="h-full flex items-center justify-center flex-1"> {/* Ensure empty state also fills height */}
            <div className="text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path vectorEffect="non-scaling-stroke" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No data found or columns configured</h3>
              <p className="mt-1 text-sm text-gray-500">
                There are no records to display for the Doctype "{doctypeDisplayName}", or list view columns are not configured for this Doctype.
              </p>
            </div>
          </div>
        )}
      </div>
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {docsToDelete.length} document(s)? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={deleteLoading}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete} disabled={deleteLoading}>
              {deleteLoading ? "Deleting..." : `Delete ${docsToDelete.length} Document(s)`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}

// Optional: To improve build times and SEO for commonly accessed Doctypes, 
// you can use generateStaticParams if you have a known list of such Doctypes.
// export async function generateStaticParams() { ... } // This is for Server Components, review if still applicable with client component approach

// Optional: Revalidate data periodically or on demand
// export const revalidate = 60; // This is for Server Components, review if still applicable

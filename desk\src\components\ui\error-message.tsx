'use client';

import React from "react";

interface ErrorMessageProps {
  message: string;
  className?: string;
}

export function ErrorMessage({ message, className }: ErrorMessageProps) {
  return (
    <div className={`flex items-center justify-center ${className || ''}`}>
      <div className="text-red-500 text-center">
        <div className="text-lg font-semibold mb-2">Error</div>
        <div className="text-sm">{message}</div>
      </div>
    </div>
  );
}

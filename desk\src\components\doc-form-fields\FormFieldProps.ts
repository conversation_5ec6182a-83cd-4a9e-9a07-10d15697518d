import { Field, DocTypeMeta } from "@/lib/frappe/frappe.types";
import { useToast } from "@/hooks/use-toast";

// Infer the return type of the toast function from the useToast hook
// This makes it more resilient to changes in use-toast.ts
type ToastSignature = ReturnType<typeof useToast>['toast'];

export interface FormFieldProps {
  field: Field;
  formData: Record<string, any>;
  handleInputChange: (fieldname: string, value: any) => void;
  isNew: boolean;
  isEditMode: boolean;
  docMeta?: DocTypeMeta | null;
  parentDoctype: string;
  toast: ToastSignature; // Use the inferred type
}

'use client';

import React, { useState, useEffect } from "react";
import { DocTypeMeta, Field } from "@/lib/frappe/frappe.types";
import { useFormLayout } from "@/hooks/useFormLayout";
import { DocFormTabs } from "./DocFormTabs";
import { DocFormSections } from "./DocFormSections";
import { DocFormFields } from "./DocFormFields";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DocFormContentProps {
  formData: Record<string, any>;
  formMeta: DocTypeMeta;
  currentDocType: any;
  doctype: string;
  isNew: boolean;
  onInputChange: (fieldname: string, value: any) => void;
}

export function DocFormContent({
  formData,
  formMeta,
  currentDocType,
  doctype,
  isNew,
  onInputChange
}: DocFormContentProps) {
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});

  // Process form layout
  const { processedLayout } = useFormLayout(currentDocType?.fields, currentDocType?.name || 'Form');

  // Set initial active tab
  useEffect(() => {
    if (processedLayout.length > 0) {
      setActiveTabId(processedLayout[0].id);
    }
  }, [processedLayout]);

  const toggleSectionCollapse = (sectionId: string) => {
    setCollapsedSections(prev => ({ ...prev, [sectionId]: !prev[sectionId] }));
  };

  const activeTabData = activeTabId ? processedLayout.find(tab => tab.id === activeTabId) : null;
  const hasMultipleTabs = processedLayout.length > 1 || 
    (processedLayout.length === 1 && processedLayout[0].label.toLowerCase() !== (currentDocType?.name?.toLowerCase() || 'form'));

  // Fallback for simple forms without layout breaks
  if (processedLayout.length === 0 && currentDocType.fields.some((f: Field) => 
    !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype)
  )) {
    return (
      <main className="flex-1 overflow-y-auto p-6 bg-slate-50">
        <div className="max-w-4xl mx-auto">
          <Card className="border-slate-200 shadow-sm bg-white">
            <CardHeader className="bg-slate-50 border-b border-slate-200">
              <CardTitle className="text-base font-semibold text-slate-700">Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <DocFormFields
                fields={currentDocType.fields}
                formData={formData}
                formMeta={formMeta}
                doctype={doctype}
                isNew={isNew}
                onInputChange={onInputChange}
              />
            </CardContent>
          </Card>
        </div>
      </main>
    );
  }

  return (
    <div className={`flex flex-1 overflow-hidden ${hasMultipleTabs ? 'flex-row' : 'flex-col'}`}>
      {/* Tabs Navigation */}
      {hasMultipleTabs && (
        <DocFormTabs
          tabs={processedLayout}
          activeTabId={activeTabId}
          onTabChange={setActiveTabId}
        />
      )}

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto p-6 bg-slate-50">
        {activeTabData ? (
          <DocFormSections
            sections={activeTabData.sections}
            formData={formData}
            formMeta={formMeta}
            doctype={doctype}
            isNew={isNew}
            collapsedSections={collapsedSections}
            onInputChange={onInputChange}
            onToggleCollapse={toggleSectionCollapse}
          />
        ) : (
          <div className="text-center py-10">
            <p className="text-slate-500">
              {processedLayout.length > 0 
                ? "Select a tab to view its content." 
                : "No fields to display for this form."
              }
            </p>
          </div>
        )}
      </main>
    </div>
  );
}

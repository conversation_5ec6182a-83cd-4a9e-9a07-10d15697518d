'use client';

import React, { useState, useEffect } from "react";
import { DocTypeMeta, Field } from "@/lib/frappe/frappe.types";
import { useFormLayout } from "@/hooks/useFormLayout";
import { DocFormTabs } from "./DocFormTabs";
import { DocFormSections } from "./DocFormSections";
import { DocFormFields } from "./DocFormFields";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Save,
  MoreHorizontal,
  Copy,
  Trash2,
  ExternalLink,
  Edit,
  Eye
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DocFormContentProps {
  formData: Record<string, any>;
  formMeta: DocTypeMeta;
  currentDocType: any;
  doctype: string;
  docname: string;
  isNew: boolean;
  isEditMode: boolean;
  onInputChange: (fieldname: string, value: any) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  onEditModeChange: (editMode: boolean) => void;
}

export function DocFormContent({
  formData,
  formMeta,
  currentDocType,
  doctype,
  docname,
  isNew,
  isEditMode,
  onInputChange,
  onSubmit,
  isSubmitting,
  onEditModeChange
}: DocFormContentProps) {
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({});

  // Process form layout
  const { processedLayout } = useFormLayout(currentDocType?.fields, currentDocType?.name || 'Form');

  // Set initial active tab
  useEffect(() => {
    if (processedLayout.length > 0) {
      setActiveTabId(processedLayout[0].id);
    }
  }, [processedLayout]);

  const toggleSectionCollapse = (sectionId: string) => {
    setCollapsedSections(prev => ({ ...prev, [sectionId]: !prev[sectionId] }));
  };

  const activeTabData = activeTabId ? processedLayout.find(tab => tab.id === activeTabId) : null;
  const hasMultipleTabs = processedLayout.length > 1 || 
    (processedLayout.length === 1 && processedLayout[0].label.toLowerCase() !== (currentDocType?.name?.toLowerCase() || 'form'));

  // Fallback for simple forms without layout breaks
  if (processedLayout.length === 0 && currentDocType.fields.some((f: Field) => 
    !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype)
  )) {
    return (
      <main className="flex-1 overflow-y-auto p-6 bg-slate-50">
        <div className="max-w-4xl mx-auto">
          <Card className="border-slate-200 shadow-sm bg-white">
            <CardHeader className="bg-slate-50 border-b border-slate-200">
              <CardTitle className="text-base font-semibold text-slate-700">Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <DocFormFields
                fields={currentDocType.fields}
                formData={formData}
                formMeta={formMeta}
                doctype={doctype}
                isNew={isNew}
                onInputChange={onInputChange}
              />
            </CardContent>
          </Card>
        </div>
      </main>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Document Header with Title and Actions */}
      <div className="bg-white border-b border-slate-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-semibold text-slate-900">
              {isNew ? `New ${doctype}` : docname}
            </h1>
            {!isNew && (
              <Badge variant="secondary" className="text-xs">
                {doctype}
              </Badge>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            {isEditMode ? (
              <>
                {/* Edit Mode Actions */}
                <Button
                  onClick={onSubmit}
                  disabled={isSubmitting}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {isSubmitting ? 'Saving...' : 'Save'}
                </Button>

                {!isNew && (
                  <Button
                    onClick={() => onEditModeChange(false)}
                    variant="outline"
                    size="sm"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    View
                  </Button>
                )}
              </>
            ) : (
              <>
                {/* View Mode Actions */}
                <Button
                  onClick={() => onEditModeChange(true)}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem>
                      <Copy className="w-4 h-4 mr-2" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open in new tab
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className={`flex flex-1 overflow-hidden ${hasMultipleTabs ? 'flex-row' : 'flex-col'}`}>
        {/* Tabs Navigation */}
        {hasMultipleTabs && (
          <DocFormTabs
            tabs={processedLayout}
            activeTabId={activeTabId}
            onTabChange={setActiveTabId}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto bg-slate-50 p-4">
          <div className="w-full max-w-none">
            {activeTabData ? (
              <DocFormSections
                sections={activeTabData.sections}
                formData={formData}
                formMeta={formMeta}
                doctype={doctype}
                isNew={isNew}
                isEditMode={isEditMode}
                collapsedSections={collapsedSections}
                onInputChange={onInputChange}
                onToggleCollapse={toggleSectionCollapse}
              />
            ) : (
              <div className="text-center py-20">
                <div className="max-w-md mx-auto bg-white rounded-2xl shadow-sm border border-slate-200 p-8">
                  <div className="text-slate-400 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-slate-900 mb-2">
                    {processedLayout.length > 0 ? "Select a tab" : "No form fields"}
                  </h3>
                  <p className="text-slate-500">
                    {processedLayout.length > 0
                      ? "Choose a tab from the sidebar to view its content."
                      : "This form doesn't have any fields to display."
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

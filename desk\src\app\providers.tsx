"use client";

import { ReactNode, useEffect, useState } from "react";
import { Toaster } from "sonner";
import { getFrappeURL } from "@/lib/frappe/frappeClient";
import LoadingSpinner from "@/components/ui/loading-spinner"; // Import LoadingSpinner

// Dynamic import for FrappeProvider to avoid SSR issues
const DynamicFrappeProvider = ({ children }: { children: ReactNode }) => {
  const [FrappeProviderComponent, setFrappeProviderComponent] = useState<any>(null); // Renamed for clarity
  const [isClient, setIsClient] = useState(false);
  const [frappeUrl, setFrappeUrl] = useState<string | null>(null); // Initialize with null

  useEffect(() => {
    setIsClient(true);
    // Get dynamic URL for multitenant after client-side hydration
    setFrappeUrl(getFrappeURL());

    // Dynamic import to avoid SSR issues
    import("frappe-react-sdk").then((module) => {
      setFrappeProviderComponent(() => module.FrappeProvider);
    }).catch(error => {
      console.error("Failed to dynamically load FrappeProvider:", error);
      // Optionally, set an error state here to inform the user or display a fallback UI
    });
  }, []);

  // If not on client, or FrappeProvider component not loaded, or URL not set,
  // show a loading state. Do not render children until provider is ready.
  if (!isClient || !FrappeProviderComponent || !frappeUrl) {
    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          {/* You can use a more sophisticated global loader here */}
          <LoadingSpinner text="Initializing App..." />
        </div>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'white',
              border: '1px solid #e5e7eb',
              color: '#374151'
            }
          }}
        />
      </>
    );
  }

  const ActualFrappeProvider = FrappeProviderComponent;

  return (
    <ActualFrappeProvider
      url={frappeUrl}
      socketPort="9000" // As per original file
      tokenParams={{
        type: "Bearer",
        useToken: false // Cookie-based auth için false, as per original file
      }}
    >
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'white',
            border: '1px solid #e5e7eb',
            color: '#374151'
          }
        }}
      />
    </ActualFrappeProvider>
  );
};

export function Providers({ children }: { children: ReactNode }) {
  return <DynamicFrappeProvider>{children}</DynamicFrappeProvider>;
}
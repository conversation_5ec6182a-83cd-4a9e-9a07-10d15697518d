'use client';

import { useParams } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { DocFormContainer } from "@/components/doc-form/DocFormContainer";

export default function DocFormPage(): JSX.Element {
  const params = useParams();
  const doctype = params.doctype as string;
  const docname = params.doc as string;

  if (!doctype || !docname) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-red-500">Invalid parameters</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <DocFormContainer
        doctype={doctype}
        docname={docname}
      />
    </AppLayout>
  );
}

'use client';

import { useParams } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";

interface DocFormPageProps {}

export default function DocFormPage(): JSX.Element {
  const params = useParams();
  const doctype = params.doctype as string;
  const docname = params.doc as string;
  const isNew = docname === 'new';

  // Decode docname for display purposes
  const decodedDocname = isNew ? docname : decodeURIComponent(docname);
  const pageTitle = isNew ? `New ${doctype}` : `Edit: ${decodedDocname} (${doctype})`;

  if (!doctype) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-red-500">Invalid doctype</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-full">
        <header className="px-6 py-4 border-b border-slate-200 bg-white shadow-sm">
          <h1 className="text-xl font-semibold text-slate-800">
            {pageTitle}
          </h1>
        </header>

        <main className="flex-1 overflow-y-auto p-6 bg-slate-50">
          <div className="max-w-4xl mx-auto">
            {/* TODO: Add DocForm component here */}
            <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
              <p className="text-slate-600">
                Document form will be implemented here.
              </p>
              <div className="mt-4 space-y-2">
                <p><strong>Doctype:</strong> {doctype}</p>
                <p><strong>Document Name:</strong> {decodedDocname}</p>
                <p><strong>Is New:</strong> {isNew ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </AppLayout>
  );
}

  // Fetch Doctype metadata
  const { data: doctypeMetaData, error: doctypeMetaError, isLoading: isLoadingMeta } = useFrappeGetCall<DocTypeMeta>(
    'frappe.desk.form.load.getdoctype',
    {
      doctype: doctype,
      with_parent: 1,
    },
    {
      enabled: !!doctype,
    }
  );

  // Fetch existing document data if not a new document
  const { data: docData, error: docError, isLoading: isLoadingDoc } = useFrappeGetDoc(
    doctype,
    docNameFromParams,
    {
      enabled: !!doctype && !isNew,
    }
  );

  // Create and Update hooks
  const { createDoc, loading: creatingDoc } = useFrappeCreateDoc();
  const { updateDoc, loading: updatingDoc } = useFrappeUpdateDoc();

  useEffect(() => {
    if (doctypeMetaData) {
      setFormMeta(doctypeMetaData);
      if (isNew && doctypeMetaData.docs?.[0]?.fields) {
        const initialData: Record<string, any> = {};
        doctypeMetaData.docs[0].fields.forEach((field: Field) => {
          if (field.default !== undefined) {
            initialData[field.fieldname] = field.default;
          } else if (field.fieldtype === 'Link' && doctypeMetaData.user_settings && typeof field.options === 'string' && doctypeMetaData.user_settings[field.options]) {
            initialData[field.fieldname] = doctypeMetaData.user_settings[field.options];
          }
        });
        setFormData(initialData);
      }
    }
    if (doctypeMetaError) {
      toast({
        title: 'Error fetching Doctype metadata',
        description: doctypeMetaError.message,
        variant: 'destructive',
      });
    }
  }, [doctypeMetaData, doctypeMetaError, isNew, toast]);

  useEffect(() => {
    if (docData) {
      setFormData(docData);
    }
    if (docError) {
      toast({
        title: 'Error fetching document data',
        description: docError.message,
        variant: 'destructive',
      });
    }
  }, [docData, docError, toast]);

  // Effect to process fields for the new layout
  const currentDocType = formMeta?.docs?.[0];
  useEffect(() => {
    if (currentDocType?.fields) {
      const layoutData = processFieldsForLayout(currentDocType.fields, currentDocType.name || 'Form');
      setProcessedLayout(layoutData);
      if (layoutData.length > 0) {
        setActiveTabId(layoutData[0].id);
      } else {
        setActiveTabId(null);
      }
    }
  }, [currentDocType]); // Removed formMeta.docs from dependency array as currentDocType covers it


  const handleInputChange = (fieldname: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldname]: value }));
  };

  const toggleSectionCollapse = (sectionId: string) => {
    setCollapsedSections(prev => ({ ...prev, [sectionId]: !prev[sectionId] }));
  };

  const handleSubmit = async () => {
    const currentDocTypeMeta = formMeta?.docs?.[0];
    if (!currentDocTypeMeta) {
      toast({ title: 'Form metadata not loaded', variant: 'destructive' });
      return;
    }

    // Basic validation for required fields
    let firstErrorField: Field | null = null;
    for (const field of currentDocTypeMeta.fields) {
      if (field.reqd && !field.hidden && (formData[field.fieldname] === undefined || formData[field.fieldname] === '' || formData[field.fieldname] === null)) {
        // Find the tab and section of the error field to expand/focus it
        for (const tab of processedLayout) {
          for (const section of tab.sections) {
            for (const col of section.columns) {
              if (col.find(f => f.fieldname === field.fieldname)) {
                setActiveTabId(tab.id);
                if (section.collapsible && collapsedSections[section.id]) {
                  toggleSectionCollapse(section.id);
                }
                firstErrorField = field;
                break;
              }
            }
            if (firstErrorField) break;
          }
          if (firstErrorField) break;
        }
        toast({
          title: 'Missing Required Field',
          description: `Please fill in "${field.label || field.fieldname}".`,
          variant: 'destructive',
        });
        // Focus the field (requires ref, simplified here)
        const fieldElement = document.getElementById(field.fieldname);
        fieldElement?.focus(); 
        return;
      }
    }

    try {
      if (isNew) {
        await createDoc(doctype, formData);
        toast({
          title: 'Document Created',
          description: `${doctype} created successfully.`,
        });
        router.push(`/app/${doctype}`);
      } else {
        await updateDoc(doctype, docNameFromParams, formData);
        toast({
          title: 'Document Updated',
          description: `${doctype} ${docNameFromParams} updated successfully.`,
        });
      }
    } catch (error: any) {
      toast({
        title: `Error ${isNew ? 'creating' : 'updating'} document`,
        description: error.message || 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  // Function to process fields into tabs, sections, and columns
  function processFieldsForLayout(fields: Field[], docTypeName: string): ProcessedTab[] {
    const resultTabs: ProcessedTab[] = [];
    if (!fields || fields.length === 0) return resultTabs;

    let currentTab: ProcessedTab | null = null;
    let currentSection: ProcessedSection | null = null;
    let currentColumnIndex = 0;

    const getSafeId = (text: string | undefined, prefix: string, counter: number) =>
      (text?.replace(/[^a-zA-Z0-9]/g, '_') || `${prefix}_${counter}`).toLowerCase() + `_${Date.now()}_${Math.random().toString(36).substring(7)}`;


    const ensureTab = (field?: Field) => {
      if (!currentTab) {
        const tabId = getSafeId(field?.fieldname, 'tab_default', resultTabs.length);
        const tabLabel = (field?.fieldtype === 'Tab Break' && field.label) ? field.label : docTypeName;
        currentTab = { id: tabId, label: tabLabel, description: field?.description, sections: [] };
        resultTabs.push(currentTab);
        currentSection = null; 
        currentColumnIndex = 0;
      }
    };

    const ensureSection = (field?: Field) => {
      ensureTab(field?.fieldtype === 'Tab Break' ? field : undefined);
      if (!currentSection) {
        const sectionId = getSafeId(field?.fieldname, 'section_default', currentTab!.sections.length);
        const sectionLabel = (field?.fieldtype === 'Section Break' && field.label) ? field.label : 'Details';
        // Ensure collapsible is treated as boolean based on 0 or 1
        currentSection = { id: sectionId, label: sectionLabel, description: field?.description, collapsible: Number(field?.collapsible) === 1, columns: [[]] };
        currentTab!.sections.push(currentSection);
        currentColumnIndex = 0;
      }
    };
    
    fields.filter(f => !f.hidden).forEach((field: Field) => {
      switch (field.fieldtype) {
        case 'Tab Break':
          const tabId = getSafeId(field.fieldname, 'tab', resultTabs.length);
          currentTab = { id: tabId, label: field.label || `Tab ${resultTabs.length + 1}`, description: field.description, sections: [] };
          resultTabs.push(currentTab);
          currentSection = null; 
          currentColumnIndex = 0;
          ensureSection(field); 
          break;
        case 'Section Break':
          ensureTab(); 
          const sectionId = getSafeId(field.fieldname, 'section', currentTab!.sections.length);
          currentSection = { 
            id: sectionId, 
            label: field.label || `Section ${currentTab!.sections.length + 1}`, 
            description: field.description, 
            // Ensure collapsible is treated as boolean based on 0 or 1
            collapsible: Number(field.collapsible) === 1,
            columns: [[]] 
          };
          currentTab!.sections.push(currentSection);
          currentColumnIndex = 0; 
          break;
        case 'Column Break':
          ensureSection(); 
          if (currentSection!.columns.length === 1 && currentSection!.columns[0].length > 0) { // Only add new column if current one has content
            currentSection!.columns.push([]);
            currentColumnIndex = currentSection!.columns.length - 1;
          } else if (currentSection!.columns.length > 1) {
             currentColumnIndex = currentSection!.columns.length -1; // Move to the last column if multiple exist
          }
          // If first column is empty, stay on it.
          break;
        default: 
          ensureSection(field); 
          if (!currentSection!.columns[currentColumnIndex]) {
            currentSection!.columns[currentColumnIndex] = []; 
          }
          currentSection!.columns[currentColumnIndex].push(field as ProcessedField);
          break;
      }
    });

    const cleanedTabs = resultTabs.map(tab => ({
      ...tab,
      sections: tab.sections.filter(section => section.columns.some(col => col.length > 0))
    })).filter(tab => tab.sections.length > 0);
    
    if (cleanedTabs.length === 0 && fields.some(f => !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype))) {
      const defaultFields = fields.filter(f => !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype));
      if (defaultFields.length > 0) {
        return [{
          id: getSafeId(undefined, 'default_tab_final', 0),
          label: docTypeName,
          sections: [{
            id: getSafeId(undefined, 'default_section_final', 0),
            label: 'Details',
            collapsible: false, // Default section not collapsible by default
            columns: [defaultFields as ProcessedField[]]
          }]
        }];
      }
    }
    return cleanedTabs;
  }


  if (isLoadingMeta || (isLoadingDoc && !isNew)) {
    return <AppLayout><div className="flex items-center justify-center h-full text-slate-500">Loading form...</div></AppLayout>;
  }

  if (!formMeta?.docs?.[0] || !currentDocType) {
    return <AppLayout><div className="flex items-center justify-center h-full text-red-500">Could not load form metadata. Please check console.</div></AppLayout>;
  }

  const renderField = (field: Field) => {
    if (field.hidden === 1) return null;
    // Read-only fields that are new should not be rendered if they don't have a default value and are not set by user_settings
    // This was simplified to just not rendering if read_only and new, which might be too aggressive.
    // Let's allow them to render but be disabled by the component itself.
    // if (field.read_only === 1 && isNew) {
    //   return null;
    // }

    if (['Section Break', 'Column Break', 'Tab Break'].includes(field.fieldtype)) {
      return null;
    }
    
    const fullFieldProps: FormFieldProps = { 
        field,
        formData,
        handleInputChange,
        isNew,
        docMeta: formMeta,
        toast: toast,
        parentDoctype: doctype,
    };


    switch (field.fieldtype) {
      case 'Data':
      case 'Password':
        return <StringInputFormField key={field.fieldname} {...fullFieldProps} />;
      case 'Int':
      case 'Float':
      case 'Currency':
      case 'Percent':
        return <NumberInputFormField key={field.fieldname} {...fullFieldProps} />;
      
      case 'Text':
      case 'Small Text':
      case 'Long Text':
      case 'JSON':
        return <TextareaFormField key={field.fieldname} {...fullFieldProps} />;
      
      case 'Check':
        const isRadioField = typeof field.options === 'string' && field.options.toLowerCase().includes('radio');
        const isSwitchField = typeof field.options === 'string' && field.options.toLowerCase().includes('switch');
        
        if (isRadioField) {
          const radioOptions = (typeof field.options === 'string' ? 
            field.options.split('\n').filter(Boolean).map(opt => {
              const parts = opt.split(':');
              const value = parts[0]?.trim() || opt.trim();
              const label = (parts[1] || parts[0])?.trim() || opt.trim();
              // Filter out the keyword 'radio' itself from being an option
              if (value.toLowerCase() === 'radio') return null;
              return { value, label };
            }).filter(Boolean) : []) as { value: string; label: string; }[]; 
          return <RadioGroupFormField key={field.fieldname} {...fullFieldProps} options={radioOptions} />;
        } else if (isSwitchField) {
          return <SwitchFormField key={field.fieldname} {...fullFieldProps} />;
        } else {
          return <CheckboxFormField key={field.fieldname} {...fullFieldProps} />;
        }

      case 'Link':
        return <LinkFormField key={field.fieldname} {...fullFieldProps} />;

      case 'Table':
        return <TablePlaceholderFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Date':
        return <DateFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Datetime':
        return <DateTimeFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Time':
        return <TimeFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Select':
        return <SelectFormField key={field.fieldname} {...fullFieldProps} />;

      default:
        return <UnsupportedFormField key={field.fieldname} {...fullFieldProps} />;
    }
  };

  const activeTabData = activeTabId ? processedLayout.find(tab => tab.id === activeTabId) : null;
  
  // Fallback if processedLayout is empty but fields exist (e.g. no layout breaks)
  if (processedLayout.length === 0 && currentDocType.fields.some(f => !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype))) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full p-4 md:p-6 bg-slate-50">
          <header className="pb-6 mb-6 border-b border-slate-200">
            <div className="flex items-center justify-between">
                <h1 className="text-xl font-semibold text-slate-800">
                {isNew ? `New ${currentDocType.name}` : `${currentDocType.name}: ${docNameFromParams}`}
                </h1>
                <Button onClick={handleSubmit} disabled={creatingDoc || updatingDoc} size="sm">
                {creatingDoc || updatingDoc ? 'Saving...' : 'Save'}
                </Button>
            </div>
          </header>
          <Card className="border-slate-200 shadow-sm bg-white">
            <CardHeader className="bg-slate-50 border-b border-slate-200 !py-3 !px-4">
              <CardTitle className="text-base font-semibold text-slate-700">Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-4">
              {currentDocType.fields.map(field => renderField(field))}
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-full max-h-screen">
        <header className="px-4 py-3 border-b border-slate-200 bg-white shadow-sm">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold text-slate-800">
                {isNew ? `New ${currentDocType?.name}` : <>Edit: <span className="font-bold">{docNameFromParams}</span> ({currentDocType?.name})</>}
            </h1>
            <Button onClick={handleSubmit} disabled={creatingDoc || updatingDoc} size="sm">
              {creatingDoc || updatingDoc ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </header>

        <div className={`flex flex-1 overflow-hidden ${processedLayout.length > 1 || (processedLayout.length === 1 && processedLayout[0].label.toLowerCase() !== (currentDocType?.name?.toLowerCase() || 'form')) ? 'flex-row' : 'flex-col'}`}>
          {/* Tabs Navigation (Sidebar) - Only if multiple tabs or a single named tab that is different from doctype name */}
          {processedLayout.length > 1 || (processedLayout.length === 1 && processedLayout[0].label.toLowerCase() !== (currentDocType?.name?.toLowerCase() || 'form')) ? (
            <nav className="w-48 md:w-56 bg-slate-100 border-r border-slate-200 overflow-y-auto p-3 space-y-1 shrink-0">
              {processedLayout.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTabId(tab.id)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-50
                    ${activeTabId === tab.id 
                      ? 'bg-sky-600 text-white font-medium shadow-sm'
                      : 'text-slate-700 hover:bg-slate-200 hover:text-slate-900 font-normal'}`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          ) : null}

          {/* Main Content Area */}
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-slate-50">
            {activeTabData ? (
              activeTabData.sections.map(section => (
                <Card key={section.id} className="mb-6 shadow-sm border-slate-200 bg-white">
                  <CardHeader className="border-b border-slate-200 !py-3 !px-4 bg-slate-50 rounded-t-md">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base font-semibold text-slate-800">{section.label}</CardTitle>
                      {section.collapsible && (
                        <Button variant="ghost" size="sm" onClick={() => toggleSectionCollapse(section.id)} className="p-1 h-auto text-slate-500 hover:text-slate-700">
                          {collapsedSections[section.id] ? <ChevronRight size={18} /> : <ChevronDown size={18} />}
                        </Button>
                      )}
                    </div>
                    {section.description && <p className="text-xs text-slate-500 mt-1">{section.description}</p>}
                  </CardHeader>
                  <Collapsible open={section.collapsible ? !collapsedSections[section.id] : true}>
                    <CollapsibleContent asChild>
                        <CardContent className="p-4">
                        <div className={`grid ${section.columns.length > 1 ? 'md:grid-cols-2 gap-x-6' : 'grid-cols-1'} gap-y-0`}>
                            {section.columns.map((column, colIdx) => (
                            <div key={colIdx} className="space-y-1 first:pt-0 last:pb-0">
                                {column.map(field => renderField(field))}
                            </div>
                            ))}
                        </div>
                        </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              ))
            ) : (
              <div className="text-center py-10">
                <p className="text-slate-500">
                  {processedLayout.length > 0 ? "Select a tab to view its content." : (currentDocType.fields.filter(f => !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype)).length > 0 ? "Form fields are available but could not be organized into tabs/sections." : "No fields to display for this form.")}
                </p>
              </div>
            )}
          </main>
        </div>
      </div>
    </AppLayout>
  );
}

import React from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';

export const UnsupportedFormField: React.FC<FormFieldProps> = ({ field }) => {
  return (
    <div className="mb-4">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>
      <Input
        id={field.fieldname}
        name={field.fieldname}
        type="text"
        disabled={true}
        value={`Field type ${field.fieldtype} not implemented`}
        className="mt-1 block w-full border border-gray-300 rounded-md p-2 bg-gray-100 text-gray-600 cursor-not-allowed"
      />
      {field.description && <p className="mt-2 text-xs text-gray-500">{field.description}</p>}
    </div>
  );
};

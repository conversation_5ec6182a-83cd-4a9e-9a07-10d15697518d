'use client';

'use client';

import React from 'react';
import {
  Bar<PERSON><PERSON> as RechartsBar<PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>ltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

export interface BarChartProps {
  data: Array<Record<string, any>>;
  bars: Array<{
    dataKey: string;
    fill?: string;
    name?: string;
    stackId?: string | number;
  }>;
  xAxisDataKey?: string;
  grid?: boolean;
  height?: number | string;
  tooltip?: boolean;
  legend?: boolean;
  layout?: 'vertical' | 'horizontal';
  yAxisFormatter?: (value: number) => string;
}

export const BarChart = ({
  data,
  bars,
  xAxisDataKey = 'name',
  grid = true,
  height = 300,
  tooltip = true,
  legend = true,
  layout = 'horizontal',
  yAxisFormatter,
}: BarChartProps) => {
  // Default colors with tailwind colors
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ];

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsBarChart
        data={data}
        layout={layout}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        {grid && <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />}
        <XAxis 
          dataKey={xAxisDataKey} 
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false}
          axisLine={{ stroke: '#e5e7eb' }}
          type={layout === 'vertical' ? 'number' : 'category'}
        />
        <YAxis 
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false} 
          axisLine={{ stroke: '#e5e7eb' }}
          tickFormatter={yAxisFormatter}
          type={layout === 'vertical' ? 'category' : 'number'}
        />
        {tooltip && <Tooltip contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', borderRadius: '0.375rem' }} />}
        {legend && <Legend wrapperStyle={{ paddingTop: '10px', fontSize: '12px' }} />}
        {bars.map((bar, index) => (
          <Bar
            key={bar.dataKey}
            dataKey={bar.dataKey}
            name={bar.name || bar.dataKey}
            fill={bar.fill || defaultColors[index % defaultColors.length]}
            stackId={bar.stackId}
            radius={[4, 4, 0, 0]}
          />
        ))}
      </RechartsBarChart>
    </ResponsiveContainer>
  );
};

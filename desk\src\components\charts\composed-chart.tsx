import React from 'react';
import {
  ComposedChart as RechartsComposed<PERSON><PERSON>,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
} from 'recharts';

export interface SeriesConfig {
  dataKey: string;
  name?: string;
  type: 'line' | 'bar' | 'area';
  color?: string;
  yAxisId?: 'left' | 'right';
  stackId?: string;
  barSize?: number;
  fillOpacity?: number;
}

export interface ComposedChartProps {
  data: Array<Record<string, any>>;
  series: SeriesConfig[];
  xAxisDataKey?: string;
  grid?: boolean;
  height?: number | string;
  tooltip?: boolean;
  legend?: boolean;
  dualYAxis?: boolean;
  leftYAxisFormatter?: (value: number) => string;
  rightYAxisFormatter?: (value: number) => string;
  leftYAxisLabel?: string;
  rightYAxisLabel?: string;
}

export const ComposedChart = ({
  data,
  series,
  xAxisDataKey = 'name',
  grid = true,
  height = 300,
  tooltip = true,
  legend = true,
  dualYAxis = false,
  leftYAxisFormatter,
  rightYAxisFormatter,
  leftYAxisLabel,
  rightYAxisLabel,
}: ComposedChartProps) => {
  // Default colors with tailwind colors
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ];

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsComposedChart
        data={data}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        {grid && <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />}
        <XAxis 
          dataKey={xAxisDataKey} 
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false}
          axisLine={{ stroke: '#e5e7eb' }}
        />
        <YAxis 
          yAxisId="left"
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false} 
          axisLine={{ stroke: '#e5e7eb' }}
          tickFormatter={leftYAxisFormatter}
          label={leftYAxisLabel ? { value: leftYAxisLabel, angle: -90, position: 'insideLeft', style: { textAnchor: 'middle' } } : undefined}
        />
        {dualYAxis && (
          <YAxis 
            yAxisId="right"
            orientation="right"
            stroke="#9ca3af" 
            fontSize={12} 
            tickLine={false} 
            axisLine={{ stroke: '#e5e7eb' }}
            tickFormatter={rightYAxisFormatter}
            label={rightYAxisLabel ? { value: rightYAxisLabel, angle: 90, position: 'insideRight', style: { textAnchor: 'middle' } } : undefined}
          />
        )}
        
        {tooltip && <Tooltip contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', borderRadius: '0.375rem' }} />}
        {legend && <Legend wrapperStyle={{ paddingTop: '10px', fontSize: '12px' }} />}
        
        {series.map((s, index) => {
          const commonProps = {
            key: s.dataKey,
            dataKey: s.dataKey,
            name: s.name || s.dataKey,
            yAxisId: s.yAxisId || 'left',
            stroke: s.color || defaultColors[index % defaultColors.length],
            fill: s.color || defaultColors[index % defaultColors.length],
          };

          if (s.type === 'line') {
            return (
              <Line
                {...commonProps}
                type="monotone"
                dot={{ r: 3 }}
                activeDot={{ r: 6 }}
                strokeWidth={2}
              />
            );
          }
          if (s.type === 'area') {
            return (
              <Area
                {...commonProps}
                type="monotone"
                strokeWidth={2}
                fillOpacity={s.fillOpacity || 0.3}
                stackId={s.stackId}
              />
            );
          }
          return (
            <Bar
              {...commonProps}
              radius={[4, 4, 0, 0]}
              barSize={s.barSize || 20}
              stackId={s.stackId}
            />
          );
        })}
      </RechartsComposedChart>
    </ResponsiveContainer>
  );
};

'use client';

import { useMemo } from "react";
import { Field } from "@/lib/frappe/frappe.types";

export interface ProcessedField extends Field {
  // any additional properties if needed during processing
}

export interface ProcessedSection {
  id: string; 
  label: string;
  description?: string;
  collapsible?: boolean;
  columns: ProcessedField[][]; // Array of columns, each column is an array of fields
}

export interface ProcessedTab {
  id: string;
  label: string;
  description?: string;
  sections: ProcessedSection[];
}

interface UseFormLayoutReturn {
  processedLayout: ProcessedTab[];
}

export function useFormLayout(
  fields: Field[] | undefined, 
  docTypeName: string
): UseFormLayoutReturn {
  const processedLayout = useMemo(() => {
    if (!fields || fields.length === 0) return [];

    return processFieldsForLayout(fields, docTypeName);
  }, [fields, docTypeName]);

  return { processedLayout };
}

function processFieldsForLayout(fields: Field[], docTypeName: string): ProcessedTab[] {
  const resultTabs: ProcessedTab[] = [];
  if (!fields || fields.length === 0) return resultTabs;

  let currentTab: ProcessedTab | null = null;
  let currentSection: ProcessedSection | null = null;
  let currentColumnIndex = 0;

  const getSafeId = (text: string | undefined, prefix: string, counter: number) =>
    (text?.replace(/[^a-zA-Z0-9]/g, '_') || `${prefix}_${counter}`).toLowerCase() + 
    `_${Date.now()}_${Math.random().toString(36).substring(7)}`;

  const ensureTab = (field?: Field) => {
    if (!currentTab) {
      const tabId = getSafeId(field?.fieldname, 'tab_default', resultTabs.length);
      const tabLabel = (field?.fieldtype === 'Tab Break' && field.label) ? field.label : docTypeName;
      currentTab = { id: tabId, label: tabLabel, description: field?.description, sections: [] };
      resultTabs.push(currentTab);
      currentSection = null; 
      currentColumnIndex = 0;
    }
  };

  const ensureSection = (field?: Field) => {
    ensureTab(field?.fieldtype === 'Tab Break' ? field : undefined);
    if (!currentSection) {
      const sectionId = getSafeId(field?.fieldname, 'section_default', currentTab!.sections.length);
      const sectionLabel = (field?.fieldtype === 'Section Break' && field.label) ? field.label : 'Details';
      currentSection = { 
        id: sectionId, 
        label: sectionLabel, 
        description: field?.description, 
        collapsible: Number(field?.collapsible) === 1, 
        columns: [[]] 
      };
      currentTab!.sections.push(currentSection);
      currentColumnIndex = 0;
    }
  };
  
  fields.filter(f => !f.hidden).forEach((field: Field) => {
    switch (field.fieldtype) {
      case 'Tab Break':
        const tabId = getSafeId(field.fieldname, 'tab', resultTabs.length);
        currentTab = { 
          id: tabId, 
          label: field.label || `Tab ${resultTabs.length + 1}`, 
          description: field.description, 
          sections: [] 
        };
        resultTabs.push(currentTab);
        currentSection = null; 
        currentColumnIndex = 0;
        ensureSection(field); 
        break;
        
      case 'Section Break':
        ensureTab(); 
        const sectionId = getSafeId(field.fieldname, 'section', currentTab!.sections.length);
        currentSection = { 
          id: sectionId, 
          label: field.label || `Section ${currentTab!.sections.length + 1}`, 
          description: field.description, 
          collapsible: Number(field.collapsible) === 1,
          columns: [[]] 
        };
        currentTab!.sections.push(currentSection);
        currentColumnIndex = 0; 
        break;
        
      case 'Column Break':
        ensureSection(); 
        if (currentSection!.columns.length === 1 && currentSection!.columns[0].length > 0) {
          currentSection!.columns.push([]);
          currentColumnIndex = currentSection!.columns.length - 1;
        } else if (currentSection!.columns.length > 1) {
          currentColumnIndex = currentSection!.columns.length - 1;
        }
        break;
        
      default: 
        ensureSection(field); 
        if (!currentSection!.columns[currentColumnIndex]) {
          currentSection!.columns[currentColumnIndex] = []; 
        }
        currentSection!.columns[currentColumnIndex].push(field as ProcessedField);
        break;
    }
  });

  // Clean up empty sections and tabs
  const cleanedTabs = resultTabs.map(tab => ({
    ...tab,
    sections: tab.sections.filter(section => section.columns.some(col => col.length > 0))
  })).filter(tab => tab.sections.length > 0);
  
  // Fallback for forms without layout breaks
  if (cleanedTabs.length === 0 && fields.some(f => 
    !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype)
  )) {
    const defaultFields = fields.filter(f => 
      !f.hidden && !['Tab Break', 'Section Break', 'Column Break'].includes(f.fieldtype)
    );
    
    if (defaultFields.length > 0) {
      return [{
        id: getSafeId(undefined, 'default_tab_final', 0),
        label: docTypeName,
        sections: [{
          id: getSafeId(undefined, 'default_section_final', 0),
          label: 'Details',
          collapsible: false,
          columns: [defaultFields as ProcessedField[]]
        }]
      }];
    }
  }
  
  return cleanedTabs;
}

'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface FormAction {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'destructive';
  className?: string;
}

interface FormActionsContextType {
  actions: FormAction[];
  setActions: (actions: FormAction[]) => void;
  clearActions: () => void;
  documentTitle?: string;
  setDocumentTitle: (title: string) => void;
  documentBadge?: string;
  setDocumentBadge: (badge: string) => void;
}

const FormActionsContext = createContext<FormActionsContextType | undefined>(undefined);

interface FormActionsProviderProps {
  children: ReactNode;
}

export function FormActionsProvider({ children }: FormActionsProviderProps) {
  const [actions, setActions] = useState<FormAction[]>([]);
  const [documentTitle, setDocumentTitle] = useState<string>('');
  const [documentBadge, setDocumentBadge] = useState<string>('');

  const clearActions = () => {
    setActions([]);
    setDocumentTitle('');
    setDocumentBadge('');
  };

  return (
    <FormActionsContext.Provider
      value={{
        actions,
        setActions,
        clearActions,
        documentTitle,
        setDocumentTitle,
        documentBadge,
        setDocumentBadge,
      }}
    >
      {children}
    </FormActionsContext.Provider>
  );
}

export function useFormActions() {
  const context = useContext(FormActionsContext);
  if (context === undefined) {
    throw new Error('useFormActions must be used within a FormActionsProvider');
  }
  return context;
}

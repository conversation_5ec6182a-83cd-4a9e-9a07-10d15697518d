'use client';

import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';

interface FormAction {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'destructive';
  className?: string;
}

interface FormActionsContextType {
  actions: FormAction[];
  setActions: (actions: FormAction[]) => void;
  clearActions: () => void;
  documentTitle?: string;
  setDocumentTitle: (title: string) => void;
  documentBadge?: string;
  setDocumentBadge: (badge: string) => void;
}

const FormActionsContext = createContext<FormActionsContextType | undefined>(undefined);

interface FormActionsProviderProps {
  children: ReactNode;
}

export function FormActionsProvider({ children }: FormActionsProviderProps) {
  const [actions, setActions] = useState<FormAction[]>([]);
  const [documentTitle, setDocumentTitle] = useState<string>('');
  const [documentBadge, setDocumentBadge] = useState<string>('');

  const clearActions = useCallback(() => {
    setActions([]);
    setDocumentTitle('');
    setDocumentBadge('');
  }, []);

  const setActionsCallback = useCallback((newActions: FormAction[]) => {
    setActions(newActions);
  }, []);

  const setDocumentTitleCallback = useCallback((title: string) => {
    setDocumentTitle(title);
  }, []);

  const setDocumentBadgeCallback = useCallback((badge: string) => {
    setDocumentBadge(badge);
  }, []);

  return (
    <FormActionsContext.Provider
      value={{
        actions,
        setActions: setActionsCallback,
        clearActions,
        documentTitle,
        setDocumentTitle: setDocumentTitleCallback,
        documentBadge,
        setDocumentBadge: setDocumentBadgeCallback,
      }}
    >
      {children}
    </FormActionsContext.Provider>
  );
}

export function useFormActions() {
  const context = useContext(FormActionsContext);
  if (context === undefined) {
    throw new Error('useFormActions must be used within a FormActionsProvider');
  }
  return context;
}

"use client";

import { AuthGuard } from "@/components/auth/auth-guard";
import EnhancedProductTable from "@/components/examples/enhanced-product-table";
import { AppLayout } from '@/components/layout/app-layout'

function DashboardContent() {
  return (
    <AppLayout>
      <EnhancedProductTable></EnhancedProductTable>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>
        <div className="grid grid-cols-1 gap-6">
        </div>
      </div>
    </AppLayout>
  );
}

export default function Dashboard() {
  return (
    <AuthGuard>
      <DashboardContent />
    </AuthGuard>
  );
}

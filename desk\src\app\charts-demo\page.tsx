"use client"

import React from 'react'
import { AppLayout } from '@/components/layout/app-layout'
import { ChartCard } from '@/components/charts/chart-card'
import { AreaChart } from '@/components/charts/area-chart'
import { <PERSON><PERSON><PERSON> } from '@/components/charts/bar-chart'
import { <PERSON><PERSON><PERSON> } from '@/components/charts/line-chart'
import { <PERSON><PERSON><PERSON> } from '@/components/charts/pie-chart'
import { Radar<PERSON>hart } from '@/components/charts/radar-chart'
import { Scatter<PERSON><PERSON> } from '@/components/charts/scatter-chart'
import { Composed<PERSON>hart } from '@/components/charts/composed-chart'
import { Funnel<PERSON>hart } from '@/components/charts/funnel-chart'
import { Gauge<PERSON><PERSON> } from '@/components/charts/gauge-chart'
import { HeatmapChart } from '@/components/charts/heatmap-chart'
import { StatsCard } from '@/components/charts/stats-card'

// Sample data for charts
const monthlyData = [
  { name: 'Jan', value: 400, revenue: 2400, profit: 1800 },
  { name: 'Feb', value: 300, revenue: 1398, profit: 1000 },
  { name: 'Mar', value: 200, revenue: 9800, profit: 5000 },
  { name: 'Apr', value: 278, revenue: 3908, profit: 2300 },
  { name: 'May', value: 189, revenue: 4800, profit: 2800 },
  { name: 'Jun', value: 239, revenue: 3800, profit: 2100 },
];

const pieData = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
];

const radarData = [
  { subject: 'Math', A: 120, B: 110, fullMark: 150 },
  { subject: 'Chinese', A: 98, B: 130, fullMark: 150 },
  { subject: 'English', A: 86, B: 130, fullMark: 150 },
  { subject: 'Physics', A: 99, B: 100, fullMark: 150 },
  { subject: 'Chemistry', A: 85, B: 90, fullMark: 150 },
];

const scatterData = [
  { x: 100, y: 200, z: 200 },
  { x: 120, y: 100, z: 260 },
  { x: 170, y: 300, z: 400 },
  { x: 140, y: 250, z: 280 },
  { x: 150, y: 400, z: 500 },
  { x: 110, y: 280, z: 200 },
];

const funnelData = [
  { value: 100, name: 'Impressions' },
  { value: 80, name: 'Visits' },
  { value: 50, name: 'Leads' },
  { value: 20, name: 'Opportunities' },
  { value: 10, name: 'Conversions' },
];

const heatmapData = Array.from({ length: 24 }, (_, hour) =>
  Array.from({ length: 7 }, (_, day) => ({
    hour,
    day,
    value: Math.floor(Math.random() * 100),
  }))
).flat();

export default function ChartsDemo() {
  return (
    <AppLayout>
      <div className="flex flex-col space-y-8">
        <h1 className="text-3xl font-bold">Charts Demo</h1>
        <p className="text-gray-500">This page demonstrates all available chart components in the IoT Gateway.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Bar Chart */}
          <ChartCard title="Bar Chart" subtitle="Monthly data visualization">
            <BarChart 
              data={monthlyData} 
              bars={[
                { dataKey: 'value', name: 'Sales' },
                { dataKey: 'revenue', name: 'Revenue' }
              ]} 
              xAxisDataKey="name"
            />
          </ChartCard>
          
          {/* Line Chart */}
          <ChartCard title="Line Chart" subtitle="Monthly trend analysis">
            <LineChart 
              data={monthlyData} 
              lines={[
                { dataKey: 'value', name: 'Sales' },
                { dataKey: 'profit', name: 'Profit' }
              ]} 
              xAxisDataKey="name"
            />
          </ChartCard>
          
          {/* Area Chart */}
          <ChartCard title="Area Chart" subtitle="Monthly area visualization">
            <AreaChart 
              data={monthlyData} 
              areas={[
                { dataKey: 'value', name: 'Sales' },
                { dataKey: 'revenue', name: 'Revenue' }
              ]} 
              xAxisDataKey="name"
            />
          </ChartCard>
          
          {/* Pie Chart */}
          <ChartCard title="Pie Chart" subtitle="Distribution by group">
            <PieChart data={pieData} />
          </ChartCard>
          
          {/* Radar Chart */}
          <ChartCard title="Radar Chart" subtitle="Comparison across categories">
            <RadarChart 
                data={radarData} 
                series={[
                    { dataKey: 'A', name: 'Student A' },
                    { dataKey: 'B', name: 'Student B' }
                ]}
                angleAxisDataKey="subject"
            />
          </ChartCard>
          
          {/* Scatter Chart */}
          <ChartCard title="Scatter Chart" subtitle="Relationship between variables">
            <ScatterChart 
              data={scatterData} 
              xAxisDataKey="x"
              yAxisDataKey="y"
              zAxisDataKey="z"
            />
          </ChartCard>
          
          {/* Composed Chart */}
          <ChartCard title="Composed Chart" subtitle="Mixed chart types">
            <ComposedChart 
              data={monthlyData}
              bars={[{ dataKey: 'revenue', name: 'Revenue' }]}
              lines={[{ dataKey: 'value', name: 'Sales' }]}
              areas={[{ dataKey: 'profit', name: 'Profit' }]}
              xAxisDataKey="name"
            />
          </ChartCard>
          
          {/* Funnel Chart */}
          <ChartCard title="Funnel Chart" subtitle="Conversion visualization">
            <FunnelChart data={funnelData} />
          </ChartCard>
          
          {/* Gauge Chart */}
          <ChartCard title="Gauge Chart" subtitle="Progress indicator">
            <GaugeChart value={75} />
          </ChartCard>
          
          {/* Heatmap Chart */}
          <ChartCard title="Heatmap Chart" subtitle="Two-dimensional data visualization">
            <HeatmapChart 
              data={heatmapData}
              xAxisDataKey="hour"
              yAxisDataKey="day"
              dataKey="value"
            />
          </ChartCard>
        </div>
        
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatsCard 
            title="Total Users" 
            value="12,345" 
            change={12}
            icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>}
          />
          <StatsCard 
            title="Revenue" 
            value="$98,765" 
            change={24}
            icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>}
          />
          <StatsCard 
            title="Active Devices" 
            value="1,234" 
            change={-5}
            icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>}
          />
        </div>
      </div>
    </AppLayout>
  )
}

import React from 'react';
import {
  <PERSON><PERSON>hart as RechartsRadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

export interface RadarChartProps {
  data: Array<Record<string, any>>;
  series: Array<{ // This is the prop in question
    dataKey: string;
    stroke?: string;
    fill?: string;
    name?: string;
  }>;
  angleAxisDataKey?: string;
  height?: number | string;
  tooltip?: boolean;
  legend?: boolean;
  valueFormatter?: (value: number) => string;
}

export const RadarChart = ({
  data,
  series,
  angleAxisDataKey = 'name',
  height = 300,
  tooltip = true,
  legend = true,
  valueFormatter,
}: RadarChartProps) => {
  // Default colors with tailwind colors
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ];

  // Add a check for series to prevent runtime error
  if (!series || !Array.isArray(series)) {
    // Optionally, you can render a placeholder or return null
    // console.error("RadarChart: 'series' prop is undefined or not an array", series);
    return null; 
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsRadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
        <PolarGrid stroke="#e5e7eb" />
        <PolarAngleAxis dataKey={angleAxisDataKey} fontSize={12} stroke="#9ca3af" />
        <PolarRadiusAxis stroke="#9ca3af" tickFormatter={valueFormatter} />
        {tooltip && <Tooltip contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', borderRadius: '0.375rem' }} />}
        {legend && <Legend wrapperStyle={{ paddingTop: '10px', fontSize: '12px' }} />}
        
        {series.map((s, index) => (
          <Radar
            key={s.dataKey}
            name={s.name || s.dataKey}
            dataKey={s.dataKey}
            stroke={s.stroke || defaultColors[index % defaultColors.length]}
            fill={s.fill || `${defaultColors[index % defaultColors.length]}40`}
            fillOpacity={0.5}
          />
        ))}
      </RechartsRadarChart>
    </ResponsiveContainer>
  );
};

# Integrating NotificationSlider with Frappe Backend

This guide explains how to integrate the NotificationSlider component with a Frappe backend to display real-time notifications in your IoT Gateway application.

## Setting Up the Frappe API Endpoint

### 1. Create a Notifications DocType

First, create a custom DocType to store user notifications:

```bash
bench make-doctype Notification
```

Configure the DocType with these fields:
- `user`: Link field to User DocType
- `type`: Select field with options (`mention`, `tags`, `access_request`, `file_review`, `article_post`, `design_view_request`)
- `status`: Select field with options (`unread`, `read`, `archived`)
- `content`: JSON field to store notification content
- `created_at`: Datetime field
- `reference_doctype`: Data field
- `reference_name`: Data field

### 2. Create API Endpoints

Create a new file in your app's API directory:

```python
# iot_gateway/api/notifications.py

import frappe
from frappe import _
import json
from datetime import datetime

@frappe.whitelist()
def get_notifications():
    """Get notifications for the current user"""
    if not frappe.session.user:
        return {"notifications": [], "unreadCount": 0}
    
    # Get unread notifications for the current user
    notifications = frappe.get_all(
        "Notification",
        filters={
            "user": frappe.session.user,
            "status": "unread"
        },
        fields=["name", "type", "content", "created_at", "reference_doctype", "reference_name"],
        order_by="created_at desc"
    )
    
    formatted_notifications = []
    for notification in notifications:
        # Parse the JSON content
        content = json.loads(notification.content)
        
        # Format notification data
        notification_data = {
            "id": notification.name,
            "type": notification.type,
            **content,
            "time": get_time_ago(notification.created_at)
        }
        
        formatted_notifications.append(notification_data)
    
    return {
        "notifications": formatted_notifications,
        "unreadCount": len(notifications)
    }

@frappe.whitelist()
def mark_all_as_read():
    """Mark all notifications as read for the current user"""
    if not frappe.session.user:
        return {"success": False, "message": "User not authenticated"}
    
    frappe.db.sql("""
        UPDATE `tabNotification`
        SET status = 'read'
        WHERE user = %s AND status = 'unread'
    """, (frappe.session.user,))
    
    frappe.db.commit()
    
    return {"success": True, "message": "All notifications marked as read"}

@frappe.whitelist()
def archive_all():
    """Archive all notifications for the current user"""
    if not frappe.session.user:
        return {"success": False, "message": "User not authenticated"}
    
    frappe.db.sql("""
        UPDATE `tabNotification`
        SET status = 'archived'
        WHERE user = %s AND status IN ('read', 'unread')
    """, (frappe.session.user,))
    
    frappe.db.commit()
    
    return {"success": True, "message": "All notifications archived"}

@frappe.whitelist()
def handle_notification_action(notification_id, action):
    """Handle notification action (Accept, Decline, etc.)"""
    if not frappe.session.user:
        return {"success": False, "message": "User not authenticated"}
    
    try:
        notification = frappe.get_doc("Notification", notification_id)
        
        # Check if notification belongs to current user
        if notification.user != frappe.session.user:
            return {"success": False, "message": "Unauthorized"}
        
        # Handle different actions based on notification type
        if notification.type == "access_request" and action in ["Accept", "Decline"]:
            # Process access request
            reference_doctype = notification.reference_doctype
            reference_name = notification.reference_name
            
            if reference_doctype and reference_name:
                # Here you would implement the logic to grant or deny access
                # This is just an example
                if action == "Accept":
                    # Grant access logic
                    pass
                else:
                    # Deny access logic
                    pass
        
        # Mark notification as read after action
        notification.status = "read"
        notification.save()
        
        return {
            "success": True, 
            "message": f"Action '{action}' performed successfully",
            "status": "read"
        }
        
    except Exception as e:
        frappe.log_error(f"Error handling notification action: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}

def get_time_ago(timestamp):
    """Convert timestamp to '5 mins ago' format"""
    now = datetime.now()
    delta = now - timestamp
    
    seconds = delta.total_seconds()
    
    if seconds < 60:
        return "just now"
    elif seconds < 3600:
        minutes = int(seconds / 60)
        return f"{minutes} min{'s' if minutes > 1 else ''} ago"
    elif seconds < 86400:
        hours = int(seconds / 3600)
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif seconds < 604800:
        days = int(seconds / 86400)
        return f"{days} day{'s' if days > 1 else ''} ago"
    else:
        return timestamp.strftime("%b %d, %Y")
```

### 3. Add Helper Function to Create Notifications

```python
# iot_gateway/utils/notification_helpers.py

import frappe
import json
from datetime import datetime

def create_notification(user, notification_type, content, reference_doctype=None, reference_name=None):
    """
    Create a new notification for a user
    
    Args:
        user: Username to send notification to
        notification_type: Type of notification (mention, access_request, etc.)
        content: Dictionary with notification content
        reference_doctype: Optional related DocType
        reference_name: Optional related document name
    """
    try:
        # Ensure user exists
        if not frappe.db.exists("User", user):
            frappe.log_error(f"Cannot create notification: User {user} does not exist")
            return None
            
        # Create notification doc
        notification = frappe.new_doc("Notification")
        notification.user = user
        notification.type = notification_type
        notification.status = "unread"
        notification.content = json.dumps(content)
        notification.created_at = datetime.now()
        
        if reference_doctype:
            notification.reference_doctype = reference_doctype
        
        if reference_name:
            notification.reference_name = reference_name
            
        notification.insert(ignore_permissions=True)
        
        return notification.name
        
    except Exception as e:
        frappe.log_error(f"Error creating notification: {str(e)}")
        return None
```

### 4. Update hooks.py to Register API endpoints

```python
# iot_gateway/hooks.py

# Add to existing hooks.py
app_include_js = [
    # other entries...
    "public/js/notification_handler.js"
]

# Define API endpoints
rest_endpoints = [
    {"route": "/api/method/iot_gateway.api.notifications.get_notifications", "methods": ["GET"]},
    {"route": "/api/method/iot_gateway.api.notifications.mark_all_as_read", "methods": ["POST"]},
    {"route": "/api/method/iot_gateway.api.notifications.archive_all", "methods": ["POST"]},
    {"route": "/api/method/iot_gateway.api.notifications.handle_notification_action", "methods": ["POST"]}
]
```

## Example Usage

### 1. Create Client-Side Integration

Create a React hook to fetch and manage notifications:

```typescript
// src/hooks/useNotifications.ts
import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

interface NotificationUser {
  name: string;
  avatarFallback: string;
  avatarUrl?: string;
}

interface BaseNotification {
  id: string;
  type: string;
  user: NotificationUser;
  action: string;
  time: string;
  context?: string;
  topic?: string;
  topicLink?: string;
}

interface MentionNotification extends BaseNotification {
  type: 'mention';
  message: string;
  replyEnabled: boolean;
}

interface AccessRequestNotification extends BaseNotification {
  type: 'access_request';
  project: string;
  actions: string[];
}

// Add more notification type interfaces as needed

type Notification = MentionNotification | AccessRequestNotification | any; // Using 'any' for simplicity

interface NotificationsResponse {
  notifications: Notification[];
  unreadCount: number;
}

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get<NotificationsResponse>(
        '/api/method/iot_gateway.api.notifications.get_notifications'
      );
      
      setNotifications(response.data.notifications);
      setUnreadCount(response.data.unreadCount);
    } catch (err) {
      setError('Failed to fetch notifications');
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      await axios.post('/api/method/iot_gateway.api.notifications.mark_all_as_read');
      setUnreadCount(0);
      
      // Update notifications to mark them as read
      setNotifications(prev => 
        prev.map(notification => ({
          ...notification,
          status: 'read'
        }))
      );
      
    } catch (err) {
      console.error('Error marking notifications as read:', err);
    }
  }, []);

  const archiveAll = useCallback(async () => {
    try {
      await axios.post('/api/method/iot_gateway.api.notifications.archive_all');
      setNotifications([]);
      setUnreadCount(0);
    } catch (err) {
      console.error('Error archiving notifications:', err);
    }
  }, []);

  const handleAction = useCallback(async (notificationId: string, action: string) => {
    try {
      const response = await axios.post('/api/method/iot_gateway.api.notifications.handle_notification_action', {
        notification_id: notificationId,
        action: action
      });
      
      if (response.data.success) {
        // Remove or update the notification in the UI
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === notificationId 
              ? { ...notification, status: 'read', actionTaken: action }
              : notification
          )
        );
      }
    } catch (err) {
      console.error(`Error handling notification action ${action}:`, err);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
    
    // Set up polling every 60 seconds
    const interval = setInterval(fetchNotifications, 60000);
    
    return () => clearInterval(interval);
  }, [fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    markAllAsRead,
    archiveAll,
    handleAction
  };
}
```

### 2. Enhance the NotificationSlider Component

Update the NotificationSlider component to work with the hook:

```tsx
// Modified version of NotificationSlider.tsx that works with the hook
import React, { useState } from 'react';
import { useNotifications } from '@/hooks/useNotifications';
// ...other imports

export function NotificationSlider() {
  const [activeTab, setActiveTab] = useState<NotificationTab>('All');
  const { 
    notifications, 
    unreadCount, 
    loading, 
    markAllAsRead, 
    archiveAll,
    handleAction 
  } = useNotifications();

  // Filter notifications based on activeTab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'Inbox') return notification.type === 'mention' || notification.type === 'file_review';
    if (activeTab === 'Team') return notification.context === 'Dev Team';
    if (activeTab === 'Following') return notification.type === 'article_post';
    return true; // 'All' shows everything
  });

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className="p-2 text-gray-500 hover:text-gray-700 relative">
          <Bell size={20} />
          {unreadCount > 0 && (
            <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-blue-600 ring-2 ring-white" />
          )}
        </button>
      </SheetTrigger>
      
      {/* ... rest of the component ... */}
      
      {/* Notification action buttons */}
      {notification.actions && (
        <div className="mt-3 space-x-2">
          {notification.actions.map(action => (
            <Button 
              key={action} 
              variant={action === 'Accept' ? 'default' : 'outline'} 
              size="sm"
              className={action === 'Accept' ? 'bg-gray-800 hover:bg-gray-900 text-white' : 'text-gray-700 border-gray-300 hover:bg-gray-50'}
              onClick={() => handleAction(notification.id, action)}
            >
              {action}
            </Button>
          ))}
        </div>
      )}
      
      {/* Footer */}
      <SheetFooter className="p-4 border-t border-gray-200 bg-slate-100 rounded-b-lg">
        <div className="flex justify-between w-full">
          <Button 
            variant="outline" 
            size="sm" 
            className="text-gray-700 border-gray-300 hover:bg-gray-100 bg-white"
            onClick={archiveAll}
          >
            Archive all
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="text-gray-700 border-gray-300 hover:bg-gray-100 bg-white"
            onClick={markAllAsRead}
          >
            Mark all as read
          </Button>
        </div>
      </SheetFooter>
    </Sheet>
  );
}
```

## Example: Creating Different Types of Notifications

Here's how to create different types of notifications from the server-side:

### Mention Notification

```python
from iot_gateway.utils.notification_helpers import create_notification

def notify_mention(mentioned_user, from_user, message, topic, topic_link, context):
    # Get user data
    user_doc = frappe.get_doc("User", from_user)
    user_info = {
        "name": user_doc.full_name,
        "avatarFallback": get_initials(user_doc.full_name)
    }
    
    # Create notification content
    content = {
        "user": user_info,
        "action": "mentioned you in",
        "topic": topic,
        "topicLink": topic_link,
        "context": context,
        "message": message,
        "replyEnabled": True
    }
    
    # Create the notification
    create_notification(
        user=mentioned_user,
        notification_type="mention",
        content=content,
        reference_doctype="Comment",  # Adjust based on your data model
        reference_name=comment_id    # Adjust based on your data model
    )

def get_initials(full_name):
    """Get initials from a full name"""
    words = full_name.split()
    return ''.join([word[0].upper() for word in words if word])[:2]
```

### Access Request Notification

```python
def notify_access_request(owner_user, requester_user, project_name, project_id):
    # Get user data
    user_doc = frappe.get_doc("User", requester_user)
    user_info = {
        "name": user_doc.full_name,
        "avatarFallback": get_initials(user_doc.full_name)
    }
    
    # Create notification content
    content = {
        "user": user_info,
        "action": "requested access to",
        "topic": project_name,
        "topicLink": f"/app/project/{project_id}",
        "project": "project",
        "context": "Dev Team",
        "actions": ["Decline", "Accept"]
    }
    
    # Create the notification
    create_notification(
        user=owner_user,
        notification_type="access_request",
        content=content,
        reference_doctype="Project",
        reference_name=project_id
    )
```

## Webhook Example for Third-party Notifications

You can integrate with external systems by creating a webhook endpoint:

```python
@frappe.whitelist(allow_guest=True)
def notification_webhook():
    """Webhook endpoint for external systems to create notifications"""
    try:
        # Get request data
        if frappe.request and frappe.request.data:
            data = json.loads(frappe.request.data)
        else:
            return {"success": False, "message": "No data provided"}
            
        # Validate webhook token/signature
        if not validate_webhook_signature(data):
            frappe.throw(_("Invalid webhook signature"))
            
        # Process notification data
        user = data.get("user")
        notification_type = data.get("type")
        content = data.get("content")
        reference = data.get("reference", {})
        
        # Create notification
        notification_id = create_notification(
            user=user,
            notification_type=notification_type,
            content=content,
            reference_doctype=reference.get("doctype"),
            reference_name=reference.get("name")
        )
        
        if notification_id:
            return {"success": True, "notification_id": notification_id}
        else:
            return {"success": False, "message": "Failed to create notification"}
            
    except Exception as e:
        frappe.log_error(f"Webhook error: {str(e)}")
        return {"success": False, "message": str(e)}
        
def validate_webhook_signature(data):
    """Validate webhook signature from request headers"""
    # Implement your validation logic here
    return True  # For demo purposes
```

## Example API Response Formats

### Success Format

```json
{
  "notifications": [
    {
      "id": "NOTIF00001",
      "type": "mention",
      "user": {
        "name": "John Smith",
        "avatarFallback": "JS"
      },
      "action": "mentioned you in",
      "topic": "Device Registration Process",
      "topicLink": "/app/device-registration/DEVREG00001",
      "time": "5 mins ago",
      "context": "IoT Devices",
      "message": "@CurrentUser Could you please review the registration flow?",
      "replyEnabled": true
    },
    {
      "id": "NOTIF00002",
      "type": "access_request",
      "user": {
        "name": "Jane Doe",
        "avatarFallback": "JD"
      },
      "action": "requested access to",
      "topic": "Temperature Sensors",
      "topicLink": "/app/project/PROJ00003",
      "project": "project",
      "time": "2 hours ago",
      "context": "IoT Team",
      "actions": ["Decline", "Accept"]
    }
  ],
  "unreadCount": 2
}
```

### Error Format

```json
{
  "success": false,
  "message": "Error description here"
}
```

This documentation should help you understand how to implement and use the NotificationSlider component with your Frappe backend.

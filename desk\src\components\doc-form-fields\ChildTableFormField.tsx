import React, { useState, useMemo } from 'react';
import { FormFieldProps } from './FormFieldProps';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  GripVertical,
  ExternalLink
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

interface ChildTableRow {
  idx: number;
  name?: string;
  [key: string]: any;
}

export const ChildTableFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  isEditMode,
  docMeta
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRowIdx, setEditingRowIdx] = useState<number | null>(null);
  const [dialogRowData, setDialogRowData] = useState<Record<string, any>>({});
  const [isNewRow, setIsNewRow] = useState(false);

  // Get child table data
  const childTableData: ChildTableRow[] = formData[field.fieldname] || [];

  // Get visible fields from docMeta or existing data
  const visibleFields = useMemo(() => {
    // Try to get child table fields from docMeta
    if (docMeta && field.options) {
      const childDocType = docMeta.docs?.find((doc: any) => doc.name === field.options);
      if (childDocType?.fields) {
        return childDocType.fields.filter((f: any) =>
          !f.hidden &&
          !['Section Break', 'Column Break', 'Tab Break'].includes(f.fieldtype) &&
          !['name', 'owner', 'creation', 'modified', 'modified_by', 'docstatus', 'idx', 'parent', 'parentfield', 'parenttype', 'doctype'].includes(f.fieldname)
        );
      }
    }

    // Fallback: Extract field names from existing data
    if (childTableData.length > 0) {
      const firstRow = childTableData[0];
      const fieldNames = Object.keys(firstRow).filter(key =>
        !['name', 'owner', 'creation', 'modified', 'modified_by', 'docstatus', 'idx', 'parent', 'parentfield', 'parenttype', 'doctype'].includes(key)
      );

      return fieldNames.map(fieldname => ({
        fieldname,
        label: fieldname.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        fieldtype: 'Data', // Default to Data type
        reqd: 0
      }));
    }

    // Default fields if no data exists
    return [
      { fieldname: 'role', label: 'Role', fieldtype: 'Data', reqd: 1 },
      { fieldname: 'description', label: 'Description', fieldtype: 'Text', reqd: 0 }
    ];
  }, [docMeta, field.options, childTableData]);

  // Open dialog for new row
  const handleOpenNewRowDialog = () => {
    setIsNewRow(true);
    setEditingRowIdx(null);
    setDialogRowData({});
    setIsDialogOpen(true);
  };

  // Open dialog for editing existing row
  const handleOpenEditDialog = (rowIndex: number) => {
    setIsNewRow(false);
    setEditingRowIdx(rowIndex);
    setDialogRowData({ ...childTableData[rowIndex] });
    setIsDialogOpen(true);
  };

  // Save dialog data
  const handleSaveDialog = () => {
    if (isNewRow) {
      // Add new row
      const newRow: ChildTableRow = {
        idx: (childTableData.length || 0) + 1,
        ...dialogRowData
      };
      const updatedData = [...childTableData, newRow];
      handleInputChange(field.fieldname, updatedData);
    } else if (editingRowIdx !== null) {
      // Update existing row
      const updatedData = [...childTableData];
      updatedData[editingRowIdx] = {
        ...updatedData[editingRowIdx],
        ...dialogRowData
      };
      handleInputChange(field.fieldname, updatedData);
    }

    setIsDialogOpen(false);
    setDialogRowData({});
    setEditingRowIdx(null);
  };

  // Cancel dialog
  const handleCancelDialog = () => {
    setIsDialogOpen(false);
    setDialogRowData({});
    setEditingRowIdx(null);
  };

  // Delete row
  const handleDeleteRow = (idx: number) => {
    const updatedData = childTableData.filter((_, index) => index !== idx);
    // Re-index remaining rows
    const reIndexedData = updatedData.map((row, index) => ({
      ...row,
      idx: index + 1
    }));
    handleInputChange(field.fieldname, reIndexedData);
  };

  // Update dialog field
  const handleDialogFieldChange = (fieldname: string, value: any) => {
    setDialogRowData(prev => ({
      ...prev,
      [fieldname]: value
    }));
  };

  // Render field input for dialog
  const renderDialogFieldInput = (childField: any, value: any) => {
    const commonProps = {
      value: value || '',
      disabled: !isEditMode,
      className: "w-full"
    };

    switch (childField.fieldtype) {
      case 'Data':
      case 'Link':
        return (
          <Input
            {...commonProps}
            onChange={(e) => handleDialogFieldChange(childField.fieldname, e.target.value)}
            placeholder={`Enter ${childField.label}`}
          />
        );

      case 'Text':
      case 'Small Text':
        return (
          <Textarea
            {...commonProps}
            rows={3}
            onChange={(e) => handleDialogFieldChange(childField.fieldname, e.target.value)}
            placeholder={`Enter ${childField.label}`}
          />
        );

      case 'Int':
      case 'Float':
      case 'Currency':
        return (
          <Input
            {...commonProps}
            type="number"
            onChange={(e) => handleDialogFieldChange(childField.fieldname, parseFloat(e.target.value) || 0)}
            placeholder={`Enter ${childField.label}`}
          />
        );

      case 'Check':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={!!value}
              onCheckedChange={(checked) => handleDialogFieldChange(childField.fieldname, checked ? 1 : 0)}
              disabled={!isEditMode}
            />
            <span className="text-sm">{childField.label}</span>
          </div>
        );

      default:
        return (
          <Input
            {...commonProps}
            onChange={(e) => handleDialogFieldChange(childField.fieldname, e.target.value)}
            placeholder={`Enter ${childField.label}`}
          />
        );
    }
  };

  // Render cell content (read-only)
  const renderCellContent = (childField: any, value: any) => {
    if (childField.fieldtype === 'Check') {
      return (
        <Checkbox
          checked={!!value}
          disabled={true}
          className="cursor-default"
        />
      );
    }
    
    if (childField.fieldtype === 'Currency') {
      return <span className="font-mono">{value ? `$${parseFloat(value).toFixed(2)}` : ''}</span>;
    }
    
    return <span className="text-sm">{value || ''}</span>;
  };

  if (!isEditMode && childTableData.length === 0) {
    return (
      <div className="mb-3">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
        </label>
        <div className="text-sm text-gray-500 italic">No data</div>
        {field.description && <p className="mt-1 text-xs text-gray-500">{field.description}</p>}
      </div>
    );
  }

  return (
    <div className="mb-3 col-span-full">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>
      
      <Card className="border border-gray-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-medium">
              {field.label} ({childTableData.length} {childTableData.length === 1 ? 'row' : 'rows'})
            </CardTitle>
            {isEditMode && (
              <Button
                onClick={handleOpenNewRowDialog}
                size="sm"
                className="h-8"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Row
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {visibleFields.length > 0 && (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">#</TableHead>
                    {visibleFields.map((childField: any) => (
                      <TableHead key={childField.fieldname} className="min-w-[120px]">
                        {childField.label}
                        {childField.reqd && <span className="text-red-500 ml-1">*</span>}
                      </TableHead>
                    ))}
                    {isEditMode && <TableHead className="w-20">Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                
                <TableBody>
                  {childTableData.map((row, rowIndex) => (
                    <TableRow key={rowIndex} className="hover:bg-gray-50">
                      <TableCell className="font-medium text-sm">
                        {row.idx || rowIndex + 1}
                      </TableCell>

                      {visibleFields.map((childField: any) => (
                        <TableCell key={childField.fieldname} className="max-w-[200px]">
                          <div className="truncate" title={row[childField.fieldname]}>
                            {renderCellContent(childField, row[childField.fieldname])}
                          </div>
                        </TableCell>
                      ))}

                      {isEditMode && (
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Button
                              onClick={() => handleOpenEditDialog(rowIndex)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                              title="Edit Row"
                            >
                              <ExternalLink className="w-3 h-3" />
                            </Button>
                            <Button
                              onClick={() => handleDeleteRow(rowIndex)}
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                              title="Delete Row"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}

                </TableBody>
              </Table>
            </div>
          )}
          
          {childTableData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p className="text-sm">No rows added yet</p>
              {isEditMode && (
                <Button
                  onClick={handleOpenNewRowDialog}
                  size="sm"
                  className="mt-2"
                  variant="outline"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add First Row
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {field.description && <p className="mt-2 text-xs text-gray-500">{field.description}</p>}

      {/* Dialog for adding/editing rows */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isNewRow ? `Add New ${field.label} Row` : `Edit ${field.label} Row`}
            </DialogTitle>
            <DialogDescription>
              {isNewRow
                ? `Fill in the details for the new ${field.label.toLowerCase()} row.`
                : `Update the details for this ${field.label.toLowerCase()} row.`
              }
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {visibleFields.map((childField: any) => (
              <div key={childField.fieldname} className="grid gap-2">
                <label className="text-sm font-medium">
                  {childField.label}
                  {childField.reqd && <span className="text-red-500 ml-1">*</span>}
                </label>
                {childField.fieldtype === 'Check' ? (
                  renderDialogFieldInput(childField, dialogRowData[childField.fieldname])
                ) : (
                  <div>
                    {renderDialogFieldInput(childField, dialogRowData[childField.fieldname])}
                  </div>
                )}
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDialog}>
              Cancel
            </Button>
            <Button onClick={handleSaveDialog} disabled={!isEditMode}>
              {isNewRow ? 'Add Row' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

import React, { useState, useMemo } from 'react';
import { FormFieldProps } from './FormFieldProps';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Plus, 
  Trash2, 
  Edit, 
  Save, 
  X,
  GripVertical 
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

interface ChildTableRow {
  idx: number;
  name?: string;
  [key: string]: any;
}

export const ChildTableFormField: React.FC<FormFieldProps> = ({
  field,
  formData,
  handleInputChange,
  isNew,
  isEditMode,
  docMeta
}) => {
  const [editingRowIdx, setEditingRowIdx] = useState<number | null>(null);
  const [newRowData, setNewRowData] = useState<Record<string, any>>({});

  // Get child table data
  const childTableData: ChildTableRow[] = formData[field.fieldname] || [];

  // Get visible fields from docMeta or existing data
  const visibleFields = useMemo(() => {
    // Try to get child table fields from docMeta
    if (docMeta && field.options) {
      const childDocType = docMeta.docs?.find((doc: any) => doc.name === field.options);
      if (childDocType?.fields) {
        return childDocType.fields.filter((f: any) =>
          !f.hidden &&
          !['Section Break', 'Column Break', 'Tab Break'].includes(f.fieldtype) &&
          !['name', 'owner', 'creation', 'modified', 'modified_by', 'docstatus', 'idx', 'parent', 'parentfield', 'parenttype', 'doctype'].includes(f.fieldname)
        );
      }
    }

    // Fallback: Extract field names from existing data
    if (childTableData.length > 0) {
      const firstRow = childTableData[0];
      const fieldNames = Object.keys(firstRow).filter(key =>
        !['name', 'owner', 'creation', 'modified', 'modified_by', 'docstatus', 'idx', 'parent', 'parentfield', 'parenttype', 'doctype'].includes(key)
      );

      return fieldNames.map(fieldname => ({
        fieldname,
        label: fieldname.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        fieldtype: 'Data', // Default to Data type
        reqd: 0
      }));
    }

    // Default fields if no data exists
    return [
      { fieldname: 'role', label: 'Role', fieldtype: 'Data', reqd: 1 },
      { fieldname: 'description', label: 'Description', fieldtype: 'Text', reqd: 0 }
    ];
  }, [docMeta, field.options, childTableData]);

  // Add new row
  const handleAddRow = () => {
    const newRow: ChildTableRow = {
      idx: (childTableData.length || 0) + 1,
      ...newRowData
    };
    
    const updatedData = [...childTableData, newRow];
    handleInputChange(field.fieldname, updatedData);
    setNewRowData({});
  };

  // Delete row
  const handleDeleteRow = (idx: number) => {
    const updatedData = childTableData.filter((_, index) => index !== idx);
    // Re-index remaining rows
    const reIndexedData = updatedData.map((row, index) => ({
      ...row,
      idx: index + 1
    }));
    handleInputChange(field.fieldname, reIndexedData);
  };

  // Update row
  const handleUpdateRow = (rowIndex: number, fieldname: string, value: any) => {
    const updatedData = [...childTableData];
    updatedData[rowIndex] = {
      ...updatedData[rowIndex],
      [fieldname]: value
    };
    handleInputChange(field.fieldname, updatedData);
  };

  // Start editing row
  const handleEditRow = (idx: number) => {
    setEditingRowIdx(idx);
  };

  // Save editing row
  const handleSaveRow = () => {
    setEditingRowIdx(null);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingRowIdx(null);
  };

  // Render field input based on fieldtype
  const renderFieldInput = (childField: any, value: any, onChange: (val: any) => void, disabled = false) => {
    const commonProps = {
      value: value || '',
      onChange,
      disabled: disabled || !isEditMode,
      className: "h-8 text-sm"
    };

    switch (childField.fieldtype) {
      case 'Data':
      case 'Link':
        return (
          <Input 
            {...commonProps}
            onChange={(e) => onChange(e.target.value)}
          />
        );
      
      case 'Text':
      case 'Small Text':
        return (
          <Textarea 
            {...commonProps}
            rows={2}
            onChange={(e) => onChange(e.target.value)}
          />
        );
      
      case 'Int':
      case 'Float':
      case 'Currency':
        return (
          <Input 
            {...commonProps}
            type="number"
            onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
          />
        );
      
      case 'Check':
        return (
          <Checkbox
            checked={!!value}
            onCheckedChange={(checked) => onChange(checked ? 1 : 0)}
            disabled={disabled || !isEditMode}
          />
        );
      
      default:
        return (
          <Input 
            {...commonProps}
            onChange={(e) => onChange(e.target.value)}
          />
        );
    }
  };

  // Render cell content (read-only)
  const renderCellContent = (childField: any, value: any) => {
    if (childField.fieldtype === 'Check') {
      return (
        <Checkbox
          checked={!!value}
          disabled={true}
          className="cursor-default"
        />
      );
    }
    
    if (childField.fieldtype === 'Currency') {
      return <span className="font-mono">{value ? `$${parseFloat(value).toFixed(2)}` : ''}</span>;
    }
    
    return <span className="text-sm">{value || ''}</span>;
  };

  if (!isEditMode && childTableData.length === 0) {
    return (
      <div className="mb-3">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
        </label>
        <div className="text-sm text-gray-500 italic">No data</div>
        {field.description && <p className="mt-1 text-xs text-gray-500">{field.description}</p>}
      </div>
    );
  }

  return (
    <div className="mb-3 col-span-full">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>
      
      <Card className="border border-gray-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-medium">
              {field.label} ({childTableData.length} {childTableData.length === 1 ? 'row' : 'rows'})
            </CardTitle>
            {isEditMode && (
              <Button 
                onClick={handleAddRow}
                size="sm" 
                className="h-8"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Row
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {visibleFields.length > 0 && (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">#</TableHead>
                    {visibleFields.map((childField: any) => (
                      <TableHead key={childField.fieldname} className="min-w-[120px]">
                        {childField.label}
                        {childField.reqd && <span className="text-red-500 ml-1">*</span>}
                      </TableHead>
                    ))}
                    {isEditMode && <TableHead className="w-20">Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                
                <TableBody>
                  {childTableData.map((row, rowIndex) => (
                    <TableRow key={rowIndex}>
                      <TableCell className="font-medium text-sm">
                        {row.idx || rowIndex + 1}
                      </TableCell>
                      
                      {visibleFields.map((childField: any) => (
                        <TableCell key={childField.fieldname}>
                          {editingRowIdx === rowIndex ? (
                            renderFieldInput(
                              childField,
                              row[childField.fieldname],
                              (value) => handleUpdateRow(rowIndex, childField.fieldname, value)
                            )
                          ) : (
                            renderCellContent(childField, row[childField.fieldname])
                          )}
                        </TableCell>
                      ))}
                      
                      {isEditMode && (
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            {editingRowIdx === rowIndex ? (
                              <>
                                <Button
                                  onClick={handleSaveRow}
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                >
                                  <Save className="w-3 h-3" />
                                </Button>
                                <Button
                                  onClick={handleCancelEdit}
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                >
                                  <X className="w-3 h-3" />
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  onClick={() => handleEditRow(rowIndex)}
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="w-3 h-3" />
                                </Button>
                                <Button
                                  onClick={() => handleDeleteRow(rowIndex)}
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                  
                  {/* Add new row form */}
                  {isEditMode && (
                    <TableRow className="bg-gray-50">
                      <TableCell className="font-medium text-sm text-gray-500">
                        {(childTableData.length || 0) + 1}
                      </TableCell>
                      
                      {visibleFields.map((childField: any) => (
                        <TableCell key={childField.fieldname}>
                          {renderFieldInput(
                            childField,
                            newRowData[childField.fieldname],
                            (value) => setNewRowData(prev => ({
                              ...prev,
                              [childField.fieldname]: value
                            }))
                          )}
                        </TableCell>
                      ))}
                      
                      <TableCell>
                        <Button
                          onClick={handleAddRow}
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
          
          {childTableData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p className="text-sm">No rows added yet</p>
              {isEditMode && (
                <Button 
                  onClick={handleAddRow}
                  size="sm" 
                  className="mt-2"
                  variant="outline"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add First Row
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {field.description && <p className="mt-2 text-xs text-gray-500">{field.description}</p>}
    </div>
  );
};

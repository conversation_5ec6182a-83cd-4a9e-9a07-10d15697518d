import React from 'react';
import {
  Line<PERSON><PERSON>,
  Line,
  ResponsiveContainer
} from 'recharts';
import { Card } from '@/components/ui/card';
import { ArrowUpIcon, ArrowDownIcon } from 'lucide-react';

export interface StatsCardProps {
  title: string;
  value: string | number;
  trend?: number;
  trendData?: Array<{ value: number }>;
  trendColor?: string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  icon?: React.ReactNode;
  description?: string;
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  trend,
  trendData = [],
  trendColor = '#3b82f6',
  prefix,
  suffix,
  icon,
  description,
  className = '',
}) => {
  // Generate random sparkline data if not provided
  const sparklineData = trendData.length > 0 
    ? trendData 
    : Array.from({ length: 10 }, () => ({
        value: Math.random() * 100
      }));
  
  // Determine the trend direction
  const trendDirection = trend != null ? trend >= 0 : null;
  
  return (
    <Card className={`p-5 ${className}`}>
      <div className="flex items-start justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <div className="mt-1 flex items-center">
            {prefix && <span className="mr-1">{prefix}</span>}
            <h3 className="text-2xl font-semibold">{value}</h3>
            {suffix && <span className="ml-1">{suffix}</span>}
          </div>
          
          {(trend != null || description) && (
            <div className="mt-2 flex items-center">
              {trend != null && (
                <div className={`flex items-center text-sm ${trendDirection ? 'text-green-500' : 'text-red-500'}`}>
                  {trendDirection ? 
                    <ArrowUpIcon className="mr-1 h-3 w-3" /> : 
                    <ArrowDownIcon className="mr-1 h-3 w-3" />
                  }
                  <span>{Math.abs(trend)}%</span>
                </div>
              )}
              {description && (
                <p className="ml-2 text-xs text-gray-500">{description}</p>
              )}
            </div>
          )}
        </div>
        
        {icon && (
          <div className="rounded-full bg-blue-50 p-2">
            {icon}
          </div>
        )}
      </div>
      
      {/* Sparkline chart */}
      <div className="h-10 mt-4">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={sparklineData}>
            <Line
              type="monotone"
              dataKey="value"
              stroke={trendColor}
              strokeWidth={2}
              dot={false}
              isAnimationActive={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};

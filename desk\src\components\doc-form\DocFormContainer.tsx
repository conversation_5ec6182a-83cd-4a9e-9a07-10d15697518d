'use client';

import React from "react";
import { useRouter } from "next/navigation";
import { useDocumentData } from "@/hooks/useDocumentData";
import { useDocumentForm } from "@/hooks/useDocumentForm";
import { DocFormHeader } from "./DocFormHeader";
import { DocFormContent } from "./DocFormContent";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { ErrorMessage } from "@/components/ui/error-message";

interface DocFormContainerProps {
  doctype: string;
  docname: string;
}

export function DocFormContainer({ doctype, docname }: DocFormContainerProps) {
  const router = useRouter();
  const isNew = docname === 'new';
  
  // Decode docname for display purposes
  const decodedDocname = isNew ? docname : decodeURIComponent(docname);
  
  // Fetch document data and metadata
  const {
    formData,
    formMeta,
    isLoading,
    error,
    currentDocType
  } = useDocumentData(doctype, docname, isNew);

  // Handle form operations
  const {
    handleInputChange,
    handleSubmit,
    isSubmitting
  } = useDocumentForm({
    doctype,
    docname: decodedDocname,
    isNew,
    formData,
    formMeta,
    onSuccess: () => {
      if (isNew) {
        router.push(`/app/${doctype}`);
      }
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message={error} />
      </div>
    );
  }

  if (!formMeta?.docs?.[0] || !currentDocType) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message="Could not load form metadata" />
      </div>
    );
  }

  // Extract metadata for display
  const lastModified = formData?.modified ? new Date(formData.modified).toLocaleDateString() : undefined;
  const modifiedBy = formData?.modified_by;

  return (
    <div className="flex flex-col h-full">
      <DocFormHeader
        doctype={doctype}
        docname={decodedDocname}
        isNew={isNew}
        onSubmit={handleSubmit}
        isSubmitting={isSubmitting}
        lastModified={lastModified}
        modifiedBy={modifiedBy}
      />

      <DocFormContent
        formData={formData}
        formMeta={formMeta}
        currentDocType={currentDocType}
        doctype={doctype}
        isNew={isNew}
        onInputChange={handleInputChange}
      />
    </div>
  );
}

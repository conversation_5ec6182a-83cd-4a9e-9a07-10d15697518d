'use client';

import React from "react";
import { useRouter } from "next/navigation";
import { useDocumentData } from "@/hooks/useDocumentData";
import { useDocumentForm } from "@/hooks/useDocumentForm";
import { DocFormContent } from "./DocFormContent";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { ErrorMessage } from "@/components/ui/error-message";

interface DocFormContainerProps {
  doctype: string;
  docname: string;
}

export function DocFormContainer({ doctype, docname }: DocFormContainerProps) {
  const router = useRouter();
  const isNew = docname === 'new';
  
  // Decode docname for display purposes
  const decodedDocname = isNew ? docname : decodeURIComponent(docname);
  
  // Fetch document data and metadata
  const {
    formData,
    formMeta,
    isLoading,
    error,
    currentDocType
  } = useDocumentData(doctype, docname, isNew);

  // Handle form operations
  const {
    handleInputChange,
    handleSubmit,
    isSubmitting
  } = useDocumentForm({
    doctype,
    docname: decodedDocname,
    isNew,
    formData,
    formMeta,
    onSuccess: () => {
      if (isNew) {
        router.push(`/app/${doctype}`);
      }
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message={error} />
      </div>
    );
  }

  if (!formMeta?.docs?.[0] || !currentDocType) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message="Could not load form metadata" />
      </div>
    );
  }

  return (
    <DocFormContent
      formData={formData}
      formMeta={formMeta}
      currentDocType={currentDocType}
      doctype={doctype}
      docname={decodedDocname}
      isNew={isNew}
      onInputChange={handleInputChange}
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
    />
  );
}

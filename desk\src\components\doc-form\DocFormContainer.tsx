'use client';

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useDocumentData } from "@/hooks/useDocumentData";
import { useDocumentForm } from "@/hooks/useDocumentForm";
import { DocFormContent } from "./DocFormContent";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { ErrorMessage } from "@/components/ui/error-message";
import { useFrappeGetDoc } from "frappe-react-sdk";

interface DocFormContainerProps {
  doctype: string;
  docname: string;
}

export function DocFormContainer({ doctype, docname }: DocFormContainerProps) {
  const router = useRouter();
  const isNew = docname === 'new';
  const [isEditMode, setIsEditMode] = useState(isNew); // New documents start in edit mode

  // Decode docname for display purposes
  const decodedDocname = isNew ? docname : decodeURIComponent(docname);

  // Fetch document data using useFrappeGetDoc for existing documents
  const {
    data: documentData,
    isLoading: isDocLoading,
    error: docError
  } = useFrappeGetDoc(
    doctype,
    isNew ? null : decodedDocname,
    isNew ? undefined : decodedDocname // Only fetch if not new
  );

  // Fetch document data and metadata
  const {
    formData,
    formMeta,
    isLoading,
    error,
    currentDocType
  } = useDocumentData(doctype, docname, isNew);

  // Handle form operations
  const {
    handleInputChange,
    handleSubmit,
    isSubmitting
  } = useDocumentForm({
    doctype,
    docname: decodedDocname,
    isNew,
    formData,
    formMeta,
    onSuccess: () => {
      if (isNew) {
        router.push(`/app/${doctype}`);
      }
    }
  });

  // Combined loading state
  const isLoadingData = isLoading || isDocLoading;

  // Combined error state
  const errorMessage = error || docError;

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  if (errorMessage) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message={errorMessage} />
      </div>
    );
  }

  if (!formMeta?.docs?.[0] || !currentDocType) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message="Could not load form metadata" />
      </div>
    );
  }

  // Use documentData for existing documents, formData for new documents
  const displayData = isNew ? formData : (documentData || formData);

  return (
    <DocFormContent
      formData={displayData}
      formMeta={formMeta}
      currentDocType={currentDocType}
      doctype={doctype}
      docname={decodedDocname}
      isNew={isNew}
      isEditMode={isEditMode}
      onInputChange={handleInputChange}
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      onEditModeChange={setIsEditMode}
    />
  );
}

'use client';

'use client';

import React from 'react';
import {
  LineChart as RechartsLine<PERSON>hart,
  Line,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';

export interface LineChartProps {
  data: Array<Record<string, any>>;
  lines: Array<{
    dataKey: string;
    stroke?: string;
    name?: string;
  }>;
  xAxisDataKey?: string;
  grid?: boolean;
  height?: number | string;
  tooltip?: boolean;
  legend?: boolean;
  yAxisFormatter?: (value: number) => string;
}

export const LineChart = ({
  data,
  lines,
  xAxisDataKey = 'name',
  grid = true,
  height = 300,
  tooltip = true,
  legend = true,
  yAxisFormatter,
}: LineChartProps) => {
  // Default colors with tailwind colors
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ];

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsLineChart
        data={data}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        {grid && <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />}
        <XAxis 
          dataKey={xAxisDataKey} 
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false}
          axisLine={{ stroke: '#e5e7eb' }}
        />
        <YAxis 
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false} 
          axisLine={{ stroke: '#e5e7eb' }}
          tickFormatter={yAxisFormatter}
        />
        {tooltip && <Tooltip contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', borderRadius: '0.375rem' }} />}
        {legend && <Legend wrapperStyle={{ paddingTop: '10px', fontSize: '12px' }} />}
        {lines.map((line, index) => (
          <Line
            key={line.dataKey}
            type="monotone"
            dataKey={line.dataKey}
            name={line.name || line.dataKey}
            stroke={line.stroke || defaultColors[index % defaultColors.length]}
            strokeWidth={2}
            dot={{ r: 3 }}
            activeDot={{ r: 6 }}
          />
        ))}
      </RechartsLineChart>
    </ResponsiveContainer>
  );
};

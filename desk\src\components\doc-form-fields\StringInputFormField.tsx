import React from 'react';
import { Input } from "@/components/ui/input";
import { FormFieldProps } from './FormFieldProps';

export const StringInputFormField: React.FC<FormFieldProps> = ({ field, formData, handleInputChange, isNew, isEditMode }) => {
  const value = formData[field.fieldname];

  // Determine if field should be readonly
  const isReadOnly = !isEditMode || (field.read_only === 1 && !isNew);

  const inputProps = {
    id: field.fieldname,
    name: field.fieldname,
    value: value === undefined ? '' : value,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleInputChange(field.fieldname, e.target.value),
    readOnly: isReadOnly,
    className: `mt-1 block w-full border border-gray-300 rounded-md p-2 ${isReadOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`,
    disabled: isReadOnly,
    type: field.fieldtype === 'Password' ? 'password' : 'text',
  };

  return (
    <div className="mb-4">
      <label htmlFor={field.fieldname} className="block text-sm font-medium text-gray-700">
        {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
      </label>
      <Input {...inputProps} />
      {field.description && <p className="mt-2 text-xs text-gray-500">{field.description}</p>}
    </div>
  );
};

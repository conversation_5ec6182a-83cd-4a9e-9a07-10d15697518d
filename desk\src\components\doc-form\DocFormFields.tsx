'use client';

import React from "react";
import { Field, DocTypeMeta } from "@/lib/frappe/frappe.types";
import { FormFieldProps } from "@/components/doc-form-fields/FormFieldProps";
import { useToast } from "@/hooks/use-toast";

// Import all form field components
import { StringInputFormField } from "@/components/doc-form-fields/StringInputFormField";
import { NumberInputFormField } from "@/components/doc-form-fields/NumberInputFormField";
import { TextareaFormField } from "@/components/doc-form-fields/TextareaFormField";
import { CheckboxFormField } from "@/components/doc-form-fields/CheckboxFormField";
import { SwitchFormField } from "@/components/doc-form-fields/SwitchFormField";
import { RadioGroupFormField } from "@/components/doc-form-fields/RadioGroupFormField";
import { SelectFormField } from "@/components/doc-form-fields/SelectFormField";
import { DateFormField } from "@/components/doc-form-fields/DateFormField";
import { DateTimeFormField } from "@/components/doc-form-fields/DateTimeFormField";
import { TimeFormField } from "@/components/doc-form-fields/TimeFormField";
import { LinkFormField } from "@/components/doc-form-fields/LinkFormField";
import { ChildTableFormField } from "@/components/doc-form-fields/ChildTableFormField";
import { UnsupportedFormField } from "@/components/doc-form-fields/UnsupportedFormField";

interface DocFormFieldsProps {
  fields: Field[];
  formData: Record<string, any>;
  formMeta: DocTypeMeta;
  doctype: string;
  isNew: boolean;
  isEditMode: boolean;
  onInputChange: (fieldname: string, value: any) => void;
}

export function DocFormFields({
  fields,
  formData,
  formMeta,
  doctype,
  isNew,
  isEditMode,
  onInputChange
}: DocFormFieldsProps) {
  const { toast } = useToast();

  const renderField = (field: Field) => {
    if (field.hidden === 1) return null;
    
    // Skip layout fields
    if (['Section Break', 'Column Break', 'Tab Break'].includes(field.fieldtype)) {
      return null;
    }
    
    const fullFieldProps: FormFieldProps = {
      field,
      formData,
      handleInputChange: onInputChange,
      isNew,
      isEditMode,
      docMeta: formMeta,
      toast,
      parentDoctype: doctype,
    };

    switch (field.fieldtype) {
      case 'Data':
      case 'Password':
        return <StringInputFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Int':
      case 'Float':
      case 'Currency':
      case 'Percent':
        return <NumberInputFormField key={field.fieldname} {...fullFieldProps} />;
      
      case 'Text':
      case 'Small Text':
      case 'Long Text':
      case 'JSON':
        return <TextareaFormField key={field.fieldname} {...fullFieldProps} />;
      
      case 'Check':
        const isRadioField = typeof field.options === 'string' && 
          field.options.toLowerCase().includes('radio');
        const isSwitchField = typeof field.options === 'string' && 
          field.options.toLowerCase().includes('switch');
        
        if (isRadioField) {
          const radioOptions = (typeof field.options === 'string' ? 
            field.options.split('\n').filter(Boolean).map(opt => {
              const parts = opt.split(':');
              const value = parts[0]?.trim() || opt.trim();
              const label = (parts[1] || parts[0])?.trim() || opt.trim();
              if (value.toLowerCase() === 'radio') return null;
              return { value, label };
            }).filter(Boolean) : []) as { value: string; label: string; }[]; 
          return <RadioGroupFormField key={field.fieldname} {...fullFieldProps} options={radioOptions} />;
        } else if (isSwitchField) {
          return <SwitchFormField key={field.fieldname} {...fullFieldProps} />;
        } else {
          return <CheckboxFormField key={field.fieldname} {...fullFieldProps} />;
        }

      case 'Link':
        return <LinkFormField key={field.fieldname} {...fullFieldProps} />;

      case 'Table':
        return <ChildTableFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Date':
        return <DateFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Datetime':
        return <DateTimeFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Time':
        return <TimeFormField key={field.fieldname} {...fullFieldProps} />;
        
      case 'Select':
        return <SelectFormField key={field.fieldname} {...fullFieldProps} />;

      default:
        return <UnsupportedFormField key={field.fieldname} {...fullFieldProps} />;
    }
  };

  return (
    <>
      {fields.map(field => renderField(field))}
    </>
  );
}

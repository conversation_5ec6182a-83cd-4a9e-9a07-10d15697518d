import React from 'react';

export interface HeatMapProps {
  data: Array<{
    x: string | number;
    y: string | number;
    value: number;
  }>;
  xLabels: string[] | number[];
  yLabels: string[] | number[];
  width?: number | string;
  height?: number | string;
  colorScale?: (value: number) => string;
  tooltip?: boolean;
  valueFormatter?: (value: number) => string;
  cellPadding?: number;
}

export const HeatMapChart = ({
  data,
  xLabels,
  yLabels,
  width = '100%',
  height = 300,
  tooltip = true,
  valueFormatter = (val) => val.toString(),
  colorScale = (value) => {
    // Default blue color scale
    const intensity = Math.min(Math.max(value, 0), 1);
    return `rgb(${Math.round(230 * (1 - intensity))}, ${Math.round(240 * (1 - intensity))}, ${Math.round(255 * (1 - 0.2 * intensity))})`;
  },
  cellPadding = 2,
}: HeatMapProps) => {
  // Create a grid for easy look-up
  const dataGrid = data.reduce((acc, item) => {
    const key = `${item.x}-${item.y}`;
    acc[key] = item.value;
    return acc;
  }, {} as Record<string, number>);

  // Find min and max values for color scaling
  const values = data.map(d => d.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const normalize = (value: number) => {
    return maxValue === minValue ? 0.5 : (value - minValue) / (maxValue - minValue);
  };

  const [tooltipData, setTooltipData] = React.useState<{
    x: number;
    y: number;
    value: number;
    xLabel: string | number;
    yLabel: string | number;
  } | null>(null);

  // Calculate cell size
  const cellWidth = `calc((100% - ${xLabels.length * cellPadding}px) / ${xLabels.length})`;
  const cellHeight = `calc((100% - ${yLabels.length * cellPadding}px) / ${yLabels.length})`;

  return (
    <div className="relative" style={{ width, height }}>
      {/* Y-axis labels */}
      <div className="absolute top-0 left-0 bottom-0 flex flex-col justify-between px-2 py-4">
        {yLabels.map((label) => (
          <div key={label} className="text-xs text-gray-500">
            {label}
          </div>
        ))}
      </div>

      {/* Heat map grid */}
      <div className="absolute inset-0 pl-10 pt-6 pr-2 pb-10">
        <div className="w-full h-full grid" 
          style={{ 
            gridTemplateColumns: `repeat(${xLabels.length}, ${cellWidth})`,
            gridTemplateRows: `repeat(${yLabels.length}, ${cellHeight})`,
            gap: `${cellPadding}px`,
          }}
        >
          {yLabels.map((yLabel, yIndex) => (
            xLabels.map((xLabel, xIndex) => {
              const key = `${xLabel}-${yLabel}`;
              const value = dataGrid[key] || 0;
              const normalizedValue = normalize(value);
              
              return (
                <div
                  key={key}
                  className="relative transition-all duration-150 cursor-pointer"
                  style={{ backgroundColor: colorScale(normalizedValue) }}
                  onMouseEnter={() => {
                    setTooltipData({
                      x: xIndex,
                      y: yIndex,
                      value,
                      xLabel,
                      yLabel,
                    });
                  }}
                  onMouseLeave={() => setTooltipData(null)}
                />
              );
            })
          ))}
        </div>

        {/* X-axis labels */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between px-1">
          {xLabels.map((label) => (
            <div key={label} className="text-xs text-gray-500 transform -translate-x-1/2">
              {label}
            </div>
          ))}
        </div>
      </div>

      {/* Tooltip */}
      {tooltip && tooltipData && (
        <div
          className="absolute z-10 px-3 py-2 text-sm bg-white shadow-lg rounded-md border border-gray-200"
          style={{
            left: `${(tooltipData.x / xLabels.length) * 100 + 10}%`,
            top: `${(tooltipData.y / yLabels.length) * 100 + 10}%`,
            transform: 'translate(-50%, -100%)',
          }}
        >
          <div className="font-medium">{`${tooltipData.xLabel}, ${tooltipData.yLabel}`}</div>
          <div className="text-gray-600">{valueFormatter(tooltipData.value)}</div>
        </div>
      )}
    </div>
  );
};

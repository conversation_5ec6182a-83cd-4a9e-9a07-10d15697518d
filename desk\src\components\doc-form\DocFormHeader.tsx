'use client';

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Save,
  MoreHorizontal,
  Copy,
  Trash2,
  ExternalLink,
  Clock,
  User
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DocFormHeaderProps {
  doctype: string;
  docname: string;
  isNew: boolean;
  onSubmit: () => void;
  isSubmitting: boolean;
  lastModified?: string;
  modifiedBy?: string;
}

export function DocFormHeader({
  doctype,
  docname,
  isNew,
  onSubmit,
  isSubmitting,
  lastModified,
  modifiedBy
}: DocFormHeaderProps) {
  const decodedDocname = isNew ? docname : decodeURIComponent(docname);

  return (
    <header className="bg-white border-b border-slate-200">
      {/* Top bar with breadcrumb and actions */}
      <div className="px-6 py-3 border-b border-slate-100">
        <div className="flex items-center justify-between">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-slate-600">
            <span className="hover:text-slate-900 cursor-pointer">{doctype}</span>
            <span>/</span>
            <span className="text-slate-900 font-medium">
              {isNew ? 'New' : decodedDocname}
            </span>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            <Button
              onClick={onSubmit}
              disabled={isSubmitting}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>

            {!isNew && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem>
                    <Copy className="w-4 h-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Open in new tab
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-red-600">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>

      {/* Document title and metadata */}
      <div className="px-6 py-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <h1 className="text-2xl font-semibold text-slate-900">
                {isNew ? `New ${doctype}` : decodedDocname}
              </h1>
              {!isNew && (
                <Badge variant="secondary" className="text-xs">
                  {doctype}
                </Badge>
              )}
            </div>

            {/* Metadata */}
            {!isNew && (lastModified || modifiedBy) && (
              <div className="flex items-center space-x-4 mt-2 text-sm text-slate-500">
                {lastModified && (
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>Modified {lastModified}</span>
                  </div>
                )}
                {modifiedBy && (
                  <div className="flex items-center space-x-1">
                    <User className="w-3 h-3" />
                    <span>by {modifiedBy}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

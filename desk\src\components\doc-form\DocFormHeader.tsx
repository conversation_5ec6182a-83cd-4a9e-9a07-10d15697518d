'use client';

import React from "react";
import { Button } from "@/components/ui/button";

interface DocFormHeaderProps {
  title: string;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export function DocFormHeader({ title, onSubmit, isSubmitting }: DocFormHeaderProps) {
  return (
    <header className="px-6 py-4 border-b border-slate-200 bg-white shadow-sm">
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-semibold text-slate-800">
          {title}
        </h1>
        <Button 
          onClick={onSubmit} 
          disabled={isSubmitting} 
          size="sm"
        >
          {isSubmitting ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </header>
  );
}

import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  ZAxis,
} from 'recharts';

export interface ScatterChartProps {
  datasets: Array<{
    name: string;
    data: Array<{
      x: number;
      y: number;
      z?: number;
      name?: string;
    }>;
    fill?: string;
  }>;
  grid?: boolean;
  height?: number | string;
  tooltip?: boolean;
  legend?: boolean;
  xAxisLabel?: string;
  yAxisLabel?: string;
  xFormatter?: (value: number) => string;
  yFormatter?: (value: number) => string;
  showZAxis?: boolean;
}

export const ScatterChart = ({
  datasets,
  grid = true,
  height = 300,
  tooltip = true,
  legend = true,
  xAxisLabel,
  yAxisLabel,
  xFormatter,
  yFormatter,
  showZAxis = false,
}: ScatterChartProps) => {
  // Default colors with tailwind colors
  const defaultColors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ];

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsScatterChart
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
      >
        {grid && <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />}
        <XAxis 
          type="number" 
          dataKey="x" 
          name={xAxisLabel} 
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false}
          axisLine={{ stroke: '#e5e7eb' }}
          tickFormatter={xFormatter}
          label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
        />
        <YAxis 
          type="number" 
          dataKey="y" 
          name={yAxisLabel}
          stroke="#9ca3af" 
          fontSize={12} 
          tickLine={false} 
          axisLine={{ stroke: '#e5e7eb' }}
          tickFormatter={yFormatter}
          label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { textAnchor: 'middle' } } : undefined}
        />
        {showZAxis && <ZAxis type="number" dataKey="z" range={[50, 500]} />}
        {tooltip && <Tooltip contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', borderRadius: '0.375rem' }} cursor={{ strokeDasharray: '3 3' }} />}
        {legend && <Legend wrapperStyle={{ paddingTop: '10px', fontSize: '12px' }} />}
        
        {datasets.map((dataset, index) => (
          <Scatter
            key={dataset.name}
            name={dataset.name}
            data={dataset.data}
            fill={dataset.fill || defaultColors[index % defaultColors.length]}
          />
        ))}
      </RechartsScatterChart>
    </ResponsiveContainer>
  );
};

'use client';

import React from 'react';
import {
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer
} from 'recharts';

export interface FunnelChartProps {
  data: Array<{
    name: string;
    value: number;
    fill?: string;
  }>;
  height?: number | string;
  width?: number | string;
  colors?: string[];
  valueFormatter?: (value: number) => string;
  nameFormatter?: (name: string) => string;
  showTooltip?: boolean;
  showLegend?: boolean;
}

export const FunnelChart: React.FC<FunnelChartProps> = ({
  data,
  height = 300,
  width = '100%',
  colors = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f97316', // orange-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ],
  valueFormatter = (value) => value.toString(),
  nameFormatter = (name) => name,
  showTooltip = true,
  showLegend = false,
}) => {
  // Sort data by value (descending)
  const sortedData = [...data].sort((a, b) => b.value - a.value);
  
  // Apply custom fills if provided in data
  const processedData = sortedData.map((item, index) => ({
    ...item,
    fill: item.fill || colors[index % colors.length],
  }));

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-md rounded-md">
          <p className="font-medium">{nameFormatter(data.name)}</p>
          <p className="text-sm text-gray-600">{valueFormatter(data.value)}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width={width} height={height}>
      <BarChart
        layout="vertical"
        data={processedData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" horizontal={false} />
        <XAxis type="number" hide />
        <YAxis 
          type="category" 
          dataKey="name" 
          tick={{ fill: '#6b7280' }} 
          tickFormatter={nameFormatter}
          axisLine={false}
          tickLine={false}
        />
        {showTooltip && <Tooltip content={<CustomTooltip />} cursor={{ fill: 'transparent' }} />}
        {showLegend && <Legend />}
        <Bar 
          dataKey="value" 
          background={{ fill: '#f3f4f6' }}
          radius={[0, 4, 4, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

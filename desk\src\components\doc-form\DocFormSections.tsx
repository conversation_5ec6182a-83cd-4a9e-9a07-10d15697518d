'use client';

import React from "react";
import { ProcessedSection } from "@/hooks/useFormLayout";
import { DocTypeMeta } from "@/lib/frappe/frappe.types";
import { DocFormFields } from "./DocFormFields";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";

interface DocFormSectionsProps {
  sections: ProcessedSection[];
  formData: Record<string, any>;
  formMeta: DocTypeMeta;
  doctype: string;
  isNew: boolean;
  collapsedSections: Record<string, boolean>;
  onInputChange: (fieldname: string, value: any) => void;
  onToggleCollapse: (sectionId: string) => void;
}

export function DocFormSections({
  sections,
  formData,
  formMeta,
  doctype,
  isNew,
  collapsedSections,
  onInputChange,
  onToggleCollapse
}: DocFormSectionsProps) {
  return (
    <div className="space-y-6">
      {sections.map(section => (
        <Card key={section.id} className="shadow-sm border-slate-200/60 bg-white overflow-hidden rounded-2xl">
          <CardHeader className="bg-white border-b border-slate-100/80 px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-semibold text-slate-900">
                  {section.label}
                </CardTitle>
                {section.description && (
                  <p className="text-sm text-slate-500 mt-2">{section.description}</p>
                )}
              </div>
              {section.collapsible && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleCollapse(section.id)}
                  className="p-3 h-auto text-slate-400 hover:text-slate-600 hover:bg-slate-50 rounded-xl transition-all duration-200"
                >
                  {collapsedSections[section.id] ?
                    <ChevronRight size={20} /> :
                    <ChevronDown size={20} />
                  }
                </Button>
              )}
            </div>
          </CardHeader>

          <Collapsible open={section.collapsible ? !collapsedSections[section.id] : true}>
            <CollapsibleContent asChild>
              <CardContent className="p-8 bg-white">
                <div className={`grid ${
                  section.columns.length > 1 ? 'lg:grid-cols-2 gap-x-12' : 'grid-cols-1'
                } gap-y-8`}>
                  {section.columns.map((column, colIdx) => (
                    <div key={colIdx} className="space-y-8">
                      <DocFormFields
                        fields={column}
                        formData={formData}
                        formMeta={formMeta}
                        doctype={doctype}
                        isNew={isNew}
                        onInputChange={onInputChange}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      ))}
    </div>
  );
}

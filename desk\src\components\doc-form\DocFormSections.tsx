'use client';

import React from "react";
import { ProcessedSection } from "@/hooks/useFormLayout";
import { DocTypeMeta } from "@/lib/frappe/frappe.types";
import { DocFormFields } from "./DocFormFields";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";

interface DocFormSectionsProps {
  sections: ProcessedSection[];
  formData: Record<string, any>;
  formMeta: DocTypeMeta;
  doctype: string;
  isNew: boolean;
  collapsedSections: Record<string, boolean>;
  onInputChange: (fieldname: string, value: any) => void;
  onToggleCollapse: (sectionId: string) => void;
}

export function DocFormSections({
  sections,
  formData,
  formMeta,
  doctype,
  isNew,
  collapsedSections,
  onInputChange,
  onToggleCollapse
}: DocFormSectionsProps) {
  return (
    <div className="space-y-6">
      {sections.map(section => (
        <Card key={section.id} className="shadow-sm border-slate-200 bg-white">
          <CardHeader className="border-b border-slate-200 bg-slate-50 rounded-t-md">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base font-semibold text-slate-800">
                {section.label}
              </CardTitle>
              {section.collapsible && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => onToggleCollapse(section.id)} 
                  className="p-1 h-auto text-slate-500 hover:text-slate-700"
                >
                  {collapsedSections[section.id] ? 
                    <ChevronRight size={18} /> : 
                    <ChevronDown size={18} />
                  }
                </Button>
              )}
            </div>
            {section.description && (
              <p className="text-xs text-slate-500 mt-1">{section.description}</p>
            )}
          </CardHeader>
          
          <Collapsible open={section.collapsible ? !collapsedSections[section.id] : true}>
            <CollapsibleContent asChild>
              <CardContent className="p-6">
                <div className={`grid ${
                  section.columns.length > 1 ? 'md:grid-cols-2 gap-x-6' : 'grid-cols-1'
                } gap-y-0`}>
                  {section.columns.map((column, colIdx) => (
                    <div key={colIdx} className="space-y-4">
                      <DocFormFields
                        fields={column}
                        formData={formData}
                        formMeta={formMeta}
                        doctype={doctype}
                        isNew={isNew}
                        onInputChange={onInputChange}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      ))}
    </div>
  );
}

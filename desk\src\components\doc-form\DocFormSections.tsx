'use client';

import React from "react";
import { ProcessedSection } from "@/hooks/useFormLayout";
import { DocTypeMeta } from "@/lib/frappe/frappe.types";
import { DocFormFields } from "./DocFormFields";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";

interface DocFormSectionsProps {
  sections: ProcessedSection[];
  formData: Record<string, any>;
  formMeta: DocTypeMeta;
  doctype: string;
  isNew: boolean;
  collapsedSections: Record<string, boolean>;
  onInputChange: (fieldname: string, value: any) => void;
  onToggleCollapse: (sectionId: string) => void;
}

export function DocFormSections({
  sections,
  formData,
  formMeta,
  doctype,
  isNew,
  collapsedSections,
  onInputChange,
  onToggleCollapse
}: DocFormSectionsProps) {
  return (
    <div className="space-y-8">
      {sections.map(section => (
        <Card key={section.id} className="shadow-sm border-slate-200 bg-white overflow-hidden">
          <CardHeader className="bg-white border-b border-slate-100 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-medium text-slate-900">
                  {section.label}
                </CardTitle>
                {section.description && (
                  <p className="text-sm text-slate-500 mt-1">{section.description}</p>
                )}
              </div>
              {section.collapsible && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleCollapse(section.id)}
                  className="p-2 h-auto text-slate-400 hover:text-slate-600 hover:bg-slate-50"
                >
                  {collapsedSections[section.id] ?
                    <ChevronRight size={20} /> :
                    <ChevronDown size={20} />
                  }
                </Button>
              )}
            </div>
          </CardHeader>

          <Collapsible open={section.collapsible ? !collapsedSections[section.id] : true}>
            <CollapsibleContent asChild>
              <CardContent className="p-6 bg-white">
                <div className={`grid ${
                  section.columns.length > 1 ? 'lg:grid-cols-2 gap-x-8' : 'grid-cols-1'
                } gap-y-6`}>
                  {section.columns.map((column, colIdx) => (
                    <div key={colIdx} className="space-y-6">
                      <DocFormFields
                        fields={column}
                        formData={formData}
                        formMeta={formMeta}
                        doctype={doctype}
                        isNew={isNew}
                        onInputChange={onInputChange}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      ))}
    </div>
  );
}

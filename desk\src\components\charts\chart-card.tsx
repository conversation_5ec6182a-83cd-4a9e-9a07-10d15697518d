'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface ChartCardProps {
  title: string;
  subtitle?: string;
  action?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  footer?: React.ReactNode;
}

export const ChartCard = ({
  title,
  subtitle,
  action,
  children,
  className = '',
  footer,
}: ChartCardProps) => {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="p-6 pb-0">
        <div className="flex items-center justify-between mb-1">
          <div>
            <h3 className="font-medium text-lg text-gray-900">{title}</h3>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
          </div>
          {action && <div>{action}</div>}
        </div>
      </div>
      
      <div className="px-6 py-4">
        {children}
      </div>
      
      {footer && (
        <>
          <Separator />
          <div className="p-4">
            {footer}
          </div>
        </>
      )}
    </Card>
  );
};

import React from 'react';
import { Checkbox } from "@/components/ui/checkbox";
import { FormFieldProps } from './FormFieldProps';

export const CheckboxFormField: React.FC<FormFieldProps> = ({ field, formData, handleInputChange, isNew, isEditMode }) => {
  const checked = !!formData[field.fieldname];
  const isDisabled = !isEditMode || (field.read_only === 1 && !isNew);

  return (
    <div className="mb-4">
      <div className="flex items-center">
        <Checkbox
          id={field.fieldname}
          name={field.fieldname}
          checked={checked}
          onCheckedChange={(isChecked) => handleInputChange(field.fieldname, isChecked ? 1 : 0)}
          disabled={isDisabled}
          className={isDisabled ? 'cursor-not-allowed' : ''}
        />
        <label htmlFor={field.fieldname} className="ml-2 block text-sm font-medium text-gray-700">
          {field.label} {field.reqd ? <span className="text-red-500">*</span> : ''}
        </label>
      </div>
      {field.description && <p className="mt-1 text-xs text-gray-500 pl-7">{field.description}</p>}
    </div>
  );
};

# Notification Slider Component Documentation

The Notification Slider is a comprehensive notification center component that displays various types of notifications in a slide-out panel. It supports different notification formats including mentions, tag updates, access requests, file reviews, and more.

## Table of Contents

- [Basic Usage](#basic-usage)
- [Component Features](#component-features)
- [Notification Types](#notification-types)
  - [Mention Notification](#mention-notification)
  - [Tags Notification](#tags-notification)
  - [Access Request Notification](#access-request-notification)
  - [File Review Notification](#file-review-notification)
  - [Article Post Notification](#article-post-notification)
  - [Design View Request Notification](#design-view-request-notification)
- [API Response Structure](#api-response-structure)
- [Customization](#customization)
- [Usage Examples](#usage-examples)

## Basic Usage

Import and use the `NotificationSlider` component in your layout:

```tsx
import { NotificationSlider } from "@/components/layout/NotificationSlider";

export function AppHeader() {
  return (
    <header className="flex justify-between items-center p-4 border-b">
      <div className="logo">My App</div>
      <div className="actions">
        <NotificationSlider />
      </div>
    </header>
  );
}
```

## Component Features

- **Categorized Views**: Filter notifications by All, Inbox, Team, or Following
- **Rich Notification Types**: Support for various notification formats
- **Interactive Elements**: Reply to mentions, accept/decline requests, view files
- **Action Buttons**: Archive all or mark all as read
- **Responsive Design**: Works well on mobile and desktop

## Notification Types

The component supports multiple notification types, each with a specific structure.

### Mention Notification

Notifications when a user mentions you in a comment or message.

**API Structure:**
```json
{
  "id": "unique-id",
  "type": "mention",
  "user": { 
    "name": "Joe Lincoln", 
    "avatarFallback": "JL",
    "avatarUrl": "optional-url-to-avatar"
  },
  "action": "mentioned you in",
  "topic": "Latest Trends",
  "topicLink": "/topics/latest-trends",
  "time": "18 mins ago",
  "context": "Web Design 2024",
  "message": "@Cody For an expert opinion, check out what Mike has to say on this topic!",
  "replyEnabled": true
}
```

### Tags Notification

Notifications about tag updates on projects or documents.

**API Structure:**
```json
{
  "id": "unique-id",
  "type": "tags",
  "user": { 
    "name": "Leslie Alexander", 
    "avatarFallback": "LA" 
  },
  "action": "added new tags to",
  "topic": "Web Redesign 2024",
  "topicLink": "/projects/web-redesign",
  "time": "53 mins ago",
  "context": "ACME",
  "tags": ["Client-Request", "Figma", "Redesign"]
}
```

### Access Request Notification

Notifications for access requests to projects or resources.

**API Structure:**
```json
{
  "id": "unique-id",
  "type": "access_request",
  "user": { 
    "name": "Guy Hawkins", 
    "avatarFallback": "GH" 
  },
  "action": "requested access to",
  "topic": "AirSpace",
  "topicLink": "/projects/airspace",
  "project": "project", 
  "time": "14 hours ago",
  "context": "Dev Team",
  "actions": ["Decline", "Accept"]
}
```

When including actions like "Accept" and "Decline", the component will automatically render action buttons. To handle these actions, you should implement event handlers in your application.

### File Review Notification

Notifications for file review requests.

**API Structure:**
```json
{
  "id": "unique-id",
  "type": "file_review",
  "user": { 
    "name": "Jane Perez", 
    "avatarFallback": "JP" 
  },
  "action": "invites you to review a file.",
  "time": "3 hours ago",
  "file": { 
    "name": "Launch_nov24.pptx", 
    "size": "742kb", 
    "edited": "Edited 39 mins ago" 
  }
}
```

### Article Post Notification

Notifications about new article posts.

**API Structure:**
```json
{
  "id": "unique-id",
  "type": "article_post",
  "user": { 
    "name": "Raymond Pawell", 
    "avatarFallback": "RP" 
  },
  "action": "posted a new article",
  "topic": "2024 Roadmap",
  "topicLink": "/articles/2024-roadmap",
  "time": "1 hour ago",
  "context": "Roadmap"
}
```

### Design View Request Notification

Notifications for design view requests.

**API Structure:**
```json
{
  "id": "unique-id",
  "type": "design_view_request",
  "user": { 
    "name": "Tyler Hero", 
    "avatarFallback": "TH" 
  },
  "action": "wants to view your design project",
  "time": "2 hours ago",
  "actions": ["Deny", "Allow"]
}
```

## API Response Structure

To populate the notifications, your API endpoint should return an array of notification objects. Each object should follow one of the structures outlined above depending on the notification type.

**Example API Response:**

```json
{
  "notifications": [
    {
      "id": "1",
      "type": "mention",
      "user": { "name": "Joe Lincoln", "avatarFallback": "JL" },
      "action": "mentioned you in",
      "topic": "Latest Trends",
      "topicLink": "/topics/latest-trends",
      "time": "18 mins ago",
      "context": "Web Design 2024",
      "message": "@Cody For an expert opinion, check out what Mike has to say on this topic!",
      "replyEnabled": true
    },
    {
      "id": "2",
      "type": "access_request",
      "user": { "name": "Guy Hawkins", "avatarFallback": "GH" },
      "action": "requested access to",
      "topic": "AirSpace",
      "topicLink": "/projects/airspace",
      "project": "project",
      "time": "14 hours ago",
      "context": "Dev Team",
      "actions": ["Decline", "Accept"]
    }
  ],
  "unreadCount": 2
}
```

## Customization

### Styling

The NotificationSlider uses Tailwind CSS classes for styling. You can customize the appearance by modifying the component or overriding the styles in your global CSS.

### Action Handlers

To handle actions like "Accept", "Decline", "Allow", or "Deny", you should modify the component to include appropriate event handlers:

```tsx
// Example handler for action buttons
const handleAction = (notificationId: string, action: string) => {
  // Implementation based on your application's needs
  if (action === "Accept") {
    // API call to accept request
    console.log(`Accepted notification ${notificationId}`);
  } else if (action === "Decline") {
    // API call to decline request
    console.log(`Declined notification ${notificationId}`);
  }
  
  // After handling the action, you might want to update the notifications list
  // by removing the processed notification or updating its status
};

// Then in your JSX where you render the action buttons:
{notification.actions && (
  <div className="mt-3 space-x-2">
    {notification.actions.map(action => (
      <Button 
        key={action} 
        variant={action === 'Accept' || action === 'Allow' ? 'default' : 'outline'} 
        size="sm"
        className={action === 'Accept' || action === 'Allow' ? 'bg-gray-800 hover:bg-gray-900 text-white' : 'text-gray-700 border-gray-300 hover:bg-gray-50'}
        onClick={() => handleAction(notification.id, action)}
      >
        {action}
      </Button>
    ))}
  </div>
)}
```

### Reply Functionality

For notifications with `replyEnabled: true`, you should implement the reply submission logic:

```tsx
// Example reply handler
const handleReply = (notificationId: string, replyText: string) => {
  // API call to submit reply
  console.log(`Replying to notification ${notificationId}: ${replyText}`);
  
  // After submission, you might want to update the UI
  // to show the submitted reply or clear the input field
};

// Then in your reply form JSX:
{notification.replyEnabled && (
  <div className="mt-2 flex items-center">
    <input 
      type="text" 
      placeholder="Reply" 
      value={replyText}
      onChange={(e) => setReplyText(e.target.value)}
      className="flex-1 text-sm p-1.5 border border-gray-300 rounded-l-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
    />
    <Button 
      variant="ghost" 
      size="icon" 
      className="p-1.5 border border-l-0 border-gray-300 rounded-r-md text-gray-500 hover:text-gray-700"
      onClick={() => handleReply(notification.id, replyText)}
    >
      <Send size={16} />
    </Button>
  </div>
)}
```

## Usage Examples

### Integrating with Real-Time Updates

To implement real-time notifications, you can use WebSockets or polling:

```tsx
import { useEffect, useState } from 'react';
import { NotificationSlider } from "@/components/layout/NotificationSlider";

export function AppHeader() {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Fetch initial notifications
  useEffect(() => {
    fetchNotifications();
  }, []);
  
  // Example polling for new notifications every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);
  
  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/notifications');
      const data = await response.json();
      setNotifications(data.notifications);
      setUnreadCount(data.unreadCount);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  };
  
  return (
    <header className="flex justify-between items-center p-4 border-b">
      <div className="logo">My App</div>
      <div className="actions">
        <NotificationSlider 
          notificationsData={notifications}
          unreadCount={unreadCount}
          onMarkAllRead={() => handleMarkAllRead()}
          onArchiveAll={() => handleArchiveAll()}
        />
      </div>
    </header>
  );
}
```

### Adding Custom Notification Types

You can extend the component to support custom notification types by adding new cases to the rendering logic:

```tsx
// In NotificationSlider.tsx
// Add a new notification type to the switch statement or conditional rendering

// For example, to add a "task_assignment" type:
{notification.type === 'task_assignment' && (
  <div className="mt-2 p-2.5 border border-gray-200 rounded-md flex items-center bg-gray-50">
    <TaskIcon size={18} className="text-blue-500 mr-2 flex-shrink-0" />
    <div>
      <p className="text-sm font-medium text-gray-800">{notification.taskName}</p>
      <p className="text-xs text-gray-500">Due: {notification.dueDate}</p>
    </div>
  </div>
)}
```

The API structure for this new type would be:

```json
{
  "id": "unique-id",
  "type": "task_assignment",
  "user": { 
    "name": "Project Manager", 
    "avatarFallback": "PM" 
  },
  "action": "assigned you a task",
  "time": "1 hour ago",
  "context": "Project XYZ",
  "taskName": "Update documentation",
  "dueDate": "June 5, 2025",
  "actions": ["Acknowledge", "Delegate"]
}
```
